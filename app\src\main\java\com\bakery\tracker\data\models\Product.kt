package com.bakery.tracker.data.models

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Represents a bakery product that can be produced and sold.
 * 
 * @param id Unique identifier for the product
 * @param name Name of the product (e.g., "Chocolate Cake", "White Bread")
 * @param description Description of the product
 * @param category Category of the product (e.g., "Cakes", "Breads", "Pastries")
 * @param sellingPrice Price at which the product is sold
 * @param costPrice Calculated cost price based on ingredients
 * @param manualCostOverride Manual override for cost price calculation
 * @param isActive Whether the product is currently being produced
 * @param createdAt Timestamp when product was added
 * @param updatedAt Timestamp when product was last updated
 */
@Entity(tableName = "products")
data class Product(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val description: String = "",
    val category: String = "",
    val sellingPrice: Double,
    val costPrice: Double = 0.0,
    val manualCostOverride: Double? = null,
    val isActive: Boolean = true,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    /**
     * Gets the effective cost price (manual override if set, otherwise calculated cost)
     */
    fun getEffectiveCostPrice(): Double = manualCostOverride ?: costPrice
    
    /**
     * Calculates profit margin percentage
     */
    fun getProfitMargin(): Double {
        val effectiveCost = getEffectiveCostPrice()
        return if (effectiveCost > 0) {
            ((sellingPrice - effectiveCost) / sellingPrice) * 100
        } else {
            0.0
        }
    }
    
    /**
     * Calculates profit amount per unit
     */
    fun getProfitAmount(): Double = sellingPrice - getEffectiveCostPrice()
}
