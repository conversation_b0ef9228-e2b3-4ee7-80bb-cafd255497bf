import React, { createContext, useContext, useState, useEffect } from 'react';
import { db } from '../database/DatabaseManager';

const DatabaseContext = createContext();

export const useDatabase = () => {
  const context = useContext(DatabaseContext);
  if (!context) {
    throw new Error('useDatabase must be used within a DatabaseProvider');
  }
  return context;
};

export const DatabaseProvider = ({ children }) => {
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    setIsReady(true);
  }, []);

  // Products operations
  const getProducts = (category = null, searchQuery = '') => {
    return new Promise((resolve, reject) => {
      let query = 'SELECT * FROM products WHERE is_active = 1';
      let params = [];

      if (category) {
        query += ' AND category = ?';
        params.push(category);
      }

      if (searchQuery) {
        query += ' AND (name LIKE ? OR description LIKE ?)';
        params.push(`%${searchQuery}%`, `%${searchQuery}%`);
      }

      query += ' ORDER BY is_popular DESC, name ASC';

      db.transaction(tx => {
        tx.executeSql(
          query,
          params,
          (_, { rows }) => resolve(rows._array),
          (_, error) => reject(error)
        );
      });
    });
  };

  const getPopularProducts = () => {
    return new Promise((resolve, reject) => {
      db.transaction(tx => {
        tx.executeSql(
          'SELECT * FROM products WHERE is_active = 1 AND is_popular = 1 ORDER BY name ASC',
          [],
          (_, { rows }) => resolve(rows._array),
          (_, error) => reject(error)
        );
      });
    });
  };

  const addProduct = (product) => {
    return new Promise((resolve, reject) => {
      db.transaction(tx => {
        tx.executeSql(
          `INSERT INTO products (name, description, category, selling_price, cost_price, servings, is_popular) 
           VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [product.name, product.description, product.category, product.sellingPrice, 
           product.costPrice, product.servings, product.isPopular ? 1 : 0],
          (_, result) => resolve(result),
          (_, error) => reject(error)
        );
      });
    });
  };

  // Recipes operations
  const getRecipes = (category = null, searchQuery = '') => {
    return new Promise((resolve, reject) => {
      let query = 'SELECT * FROM recipes WHERE is_active = 1';
      let params = [];

      if (category) {
        query += ' AND category = ?';
        params.push(category);
      }

      if (searchQuery) {
        query += ' AND (name LIKE ? OR instructions LIKE ?)';
        params.push(`%${searchQuery}%`, `%${searchQuery}%`);
      }

      query += ' ORDER BY name ASC';

      db.transaction(tx => {
        tx.executeSql(
          query,
          params,
          (_, { rows }) => resolve(rows._array),
          (_, error) => reject(error)
        );
      });
    });
  };

  const addRecipe = (recipe) => {
    return new Promise((resolve, reject) => {
      db.transaction(tx => {
        tx.executeSql(
          `INSERT INTO recipes (name, instructions, servings, prep_time, cook_time, difficulty, category) 
           VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [recipe.name, recipe.instructions, recipe.servings, recipe.prepTime, 
           recipe.cookTime, recipe.difficulty, recipe.category],
          (_, result) => resolve(result),
          (_, error) => reject(error)
        );
      });
    });
  };

  const deleteRecipe = (recipeId) => {
    return new Promise((resolve, reject) => {
      db.transaction(tx => {
        tx.executeSql(
          'UPDATE recipes SET is_active = 0 WHERE id = ?',
          [recipeId],
          (_, result) => resolve(result),
          (_, error) => reject(error)
        );
      });
    });
  };

  // Orders operations
  const getOrders = (limit = 50) => {
    return new Promise((resolve, reject) => {
      db.transaction(tx => {
        tx.executeSql(
          'SELECT * FROM orders ORDER BY created_at DESC LIMIT ?',
          [limit],
          (_, { rows }) => resolve(rows._array),
          (_, error) => reject(error)
        );
      });
    });
  };

  const getTodayOrders = () => {
    return new Promise((resolve, reject) => {
      const today = new Date().toISOString().split('T')[0];
      db.transaction(tx => {
        tx.executeSql(
          'SELECT * FROM orders WHERE DATE(created_at) = ? ORDER BY created_at DESC',
          [today],
          (_, { rows }) => resolve(rows._array),
          (_, error) => reject(error)
        );
      });
    });
  };

  // Analytics operations
  const getDailySales = () => {
    return new Promise((resolve, reject) => {
      const today = new Date().toISOString().split('T')[0];
      db.transaction(tx => {
        tx.executeSql(
          'SELECT COALESCE(SUM(total), 0) as total FROM orders WHERE DATE(created_at) = ?',
          [today],
          (_, { rows }) => resolve(rows._array[0].total),
          (_, error) => reject(error)
        );
      });
    });
  };

  const getWeeklySales = () => {
    return new Promise((resolve, reject) => {
      db.transaction(tx => {
        tx.executeSql(
          `SELECT 
             DATE(created_at) as date,
             COALESCE(SUM(total), 0) as total,
             COUNT(*) as order_count
           FROM orders 
           WHERE DATE(created_at) >= DATE('now', '-7 days')
           GROUP BY DATE(created_at)
           ORDER BY date ASC`,
          [],
          (_, { rows }) => resolve(rows._array),
          (_, error) => reject(error)
        );
      });
    });
  };

  const getTopSellingProducts = (limit = 5) => {
    return new Promise((resolve, reject) => {
      db.transaction(tx => {
        tx.executeSql(
          `SELECT 
             oi.product_name,
             SUM(oi.quantity) as total_quantity,
             SUM(oi.total) as total_revenue
           FROM order_items oi
           JOIN orders o ON oi.order_id = o.id
           WHERE DATE(o.created_at) >= DATE('now', '-30 days')
           GROUP BY oi.product_id, oi.product_name
           ORDER BY total_quantity DESC
           LIMIT ?`,
          [limit],
          (_, { rows }) => resolve(rows._array),
          (_, error) => reject(error)
        );
      });
    });
  };

  // Production operations
  const addProduction = (production) => {
    return new Promise((resolve, reject) => {
      db.transaction(tx => {
        tx.executeSql(
          `INSERT INTO production (product_id, recipe_id, quantity_produced, production_date, batch_number, notes) 
           VALUES (?, ?, ?, ?, ?, ?)`,
          [production.productId, production.recipeId, production.quantityProduced, 
           production.productionDate, production.batchNumber, production.notes],
          (_, result) => resolve(result),
          (_, error) => reject(error)
        );
      });
    });
  };

  const getTodayProduction = () => {
    return new Promise((resolve, reject) => {
      const today = new Date().toISOString().split('T')[0];
      db.transaction(tx => {
        tx.executeSql(
          `SELECT 
             p.product_name,
             SUM(pr.quantity_produced) as total_produced
           FROM production pr
           JOIN products p ON pr.product_id = p.id
           WHERE DATE(pr.production_date) = ?
           GROUP BY pr.product_id, p.product_name
           ORDER BY total_produced DESC`,
          [today],
          (_, { rows }) => resolve(rows._array),
          (_, error) => reject(error)
        );
      });
    });
  };

  const value = {
    isReady,
    // Products
    getProducts,
    getPopularProducts,
    addProduct,
    // Recipes
    getRecipes,
    addRecipe,
    deleteRecipe,
    // Orders
    getOrders,
    getTodayOrders,
    // Analytics
    getDailySales,
    getWeeklySales,
    getTopSellingProducts,
    // Production
    addProduction,
    getTodayProduction
  };

  return (
    <DatabaseContext.Provider value={value}>
      {children}
    </DatabaseContext.Provider>
  );
};
