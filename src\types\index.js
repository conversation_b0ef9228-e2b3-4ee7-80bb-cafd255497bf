// Product Categories (exactly as per overview.txt)
export const ProductCategory = {
  CAKES: 'CAKES',
  PASTRIES: 'PASTRIES', 
  BREADS: 'BREADS',
  BEVERAGES: 'BEVERAGES'
};

export const ProductCategoryDisplayNames = {
  [ProductCategory.CAKES]: 'Cakes',
  [ProductCategory.PASTRIES]: 'Pastries',
  [ProductCategory.BREADS]: 'Breads',
  [ProductCategory.BEVERAGES]: 'Beverages'
};

// Recipe Difficulty Levels
export const RecipeDifficulty = {
  EASY: 'EASY',
  MEDIUM: 'MEDIUM',
  HARD: 'HARD'
};

export const RecipeDifficultyDisplayNames = {
  [RecipeDifficulty.EASY]: 'Easy',
  [RecipeDifficulty.MEDIUM]: 'Medium',
  [RecipeDifficulty.HARD]: 'Hard'
};

// Payment Methods (as per overview.txt)
export const PaymentMethod = {
  CASH: 'CASH',
  CARD: 'CARD',
  UPI: 'UPI'
};

export const PaymentMethodDisplayNames = {
  [PaymentMethod.CASH]: 'Cash',
  [PaymentMethod.CARD]: 'Card',
  [PaymentMethod.UPI]: 'UPI'
};

// Order Status
export const OrderStatus = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

// User Roles (as per overview.txt)
export const UserRole = {
  ADMIN: 'ADMIN',
  STAFF: 'STAFF'
};

// Ingredient Units
export const IngredientUnit = {
  KG: 'kg',
  GRAMS: 'grams',
  LITERS: 'liters',
  ML: 'ml',
  PIECES: 'pieces',
  CUPS: 'cups',
  TBSP: 'tbsp',
  TSP: 'tsp'
};

// Sample Data Structure Types
export const createProduct = (data) => ({
  id: data.id || Date.now(),
  name: data.name,
  description: data.description || '',
  category: data.category,
  sellingPrice: data.sellingPrice,
  costPrice: data.costPrice || 0,
  servings: data.servings || 1,
  isPopular: data.isPopular || false,
  isActive: data.isActive !== false,
  createdAt: data.createdAt || new Date().toISOString(),
  updatedAt: data.updatedAt || new Date().toISOString()
});

export const createRecipe = (data) => ({
  id: data.id || Date.now(),
  productId: data.productId || null,
  name: data.name,
  instructions: data.instructions || '',
  servings: data.servings || 1,
  prepTime: data.prepTime || 0,
  cookTime: data.cookTime || 0,
  difficulty: data.difficulty || RecipeDifficulty.MEDIUM,
  category: data.category,
  isActive: data.isActive !== false,
  createdAt: data.createdAt || new Date().toISOString(),
  updatedAt: data.updatedAt || new Date().toISOString()
});

export const createCartItem = (data) => ({
  id: data.id || Date.now(),
  productId: data.productId,
  productName: data.productName,
  price: data.price,
  quantity: data.quantity || 1,
  category: data.category,
  isPopular: data.isPopular || false,
  sessionId: data.sessionId,
  createdAt: data.createdAt || new Date().toISOString()
});

export const createOrder = (data) => ({
  id: data.id || Date.now(),
  billNumber: data.billNumber,
  customerName: data.customerName || '',
  customerPhone: data.customerPhone || '',
  subtotal: data.subtotal,
  discount: data.discount || 0,
  tax: data.tax || 0,
  total: data.total,
  paymentMethod: data.paymentMethod,
  paymentStatus: data.paymentStatus || OrderStatus.COMPLETED,
  createdAt: data.createdAt || new Date().toISOString(),
  updatedAt: data.updatedAt || new Date().toISOString()
});

export const createCustomer = (data) => ({
  id: data.id || Date.now(),
  name: data.name,
  phone: data.phone,
  email: data.email || '',
  address: data.address || '',
  totalOrders: data.totalOrders || 0,
  totalSpent: data.totalSpent || 0,
  createdAt: data.createdAt || new Date().toISOString(),
  updatedAt: data.updatedAt || new Date().toISOString()
});

// Cart Summary Calculator (as per overview.txt)
export const calculateCartSummary = (cartItems) => {
  const subtotal = cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  const itemCount = cartItems.reduce((total, item) => total + item.quantity, 0);
  const uniqueProductCount = cartItems.length;
  
  return {
    items: cartItems,
    subtotal,
    itemCount,
    uniqueProductCount
  };
};

// Profit Margin Calculator (as per overview.txt)
export const calculateProfitMargin = (sellingPrice, costPrice) => {
  if (sellingPrice <= 0) return 0;
  return ((sellingPrice - costPrice) / sellingPrice) * 100;
};

// Suggested Pricing (as per overview.txt)
export const getSuggestedPricing = (costPrice) => ({
  margin30: costPrice * 1.43, // 30% margin
  margin40: costPrice * 1.67, // 40% margin
  margin50: costPrice * 2.0   // 50% margin
});

// Low Stock Detection (as per overview.txt)
export const isLowStock = (currentStock, minimumThreshold) => {
  return currentStock < minimumThreshold;
};

// Bill Number Generator (as per overview.txt)
export const generateBillNumber = () => {
  const date = new Date();
  const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
  const random = Math.floor(1000 + Math.random() * 9000);
  return `BT-${dateStr}-${random}`;
};
