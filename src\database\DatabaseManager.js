import * as SQLite from 'expo-sqlite';
import { 
  ProductCategory, 
  RecipeDifficulty, 
  PaymentMethod, 
  OrderStatus,
  createProduct,
  createRecipe 
} from '../types';

const db = SQLite.openDatabase('bakery_tracker.db');

// Initialize database with all tables (as per overview.txt)
export const initializeDatabase = () => {
  return new Promise((resolve, reject) => {
    db.transaction(tx => {
      // Products table
      tx.executeSql(`
        CREATE TABLE IF NOT EXISTS products (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          description TEXT,
          category TEXT NOT NULL,
          selling_price REAL NOT NULL,
          cost_price REAL DEFAULT 0,
          servings INTEGER DEFAULT 1,
          is_popular BOOLEAN DEFAULT 0,
          is_active BOOLEAN DEFAULT 1,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        );
      `);

      // Recipes table
      tx.executeSql(`
        CREATE TABLE IF NOT EXISTS recipes (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          product_id INTEGER,
          name TEXT NOT NULL,
          instructions TEXT,
          servings INTEGER DEFAULT 1,
          prep_time INTEGER DEFAULT 0,
          cook_time INTEGER DEFAULT 0,
          difficulty TEXT DEFAULT 'MEDIUM',
          category TEXT NOT NULL,
          is_active BOOLEAN DEFAULT 1,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (product_id) REFERENCES products (id)
        );
      `);

      // Cart table
      tx.executeSql(`
        CREATE TABLE IF NOT EXISTS cart (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          product_id INTEGER NOT NULL,
          product_name TEXT NOT NULL,
          price REAL NOT NULL,
          quantity INTEGER NOT NULL,
          category TEXT NOT NULL,
          is_popular BOOLEAN DEFAULT 0,
          session_id TEXT NOT NULL,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (product_id) REFERENCES products (id)
        );
      `);

      // Orders table
      tx.executeSql(`
        CREATE TABLE IF NOT EXISTS orders (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          bill_number TEXT UNIQUE NOT NULL,
          customer_name TEXT,
          customer_phone TEXT,
          subtotal REAL NOT NULL,
          discount REAL DEFAULT 0,
          tax REAL DEFAULT 0,
          total REAL NOT NULL,
          payment_method TEXT NOT NULL,
          payment_status TEXT DEFAULT 'COMPLETED',
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        );
      `);

      // Order Items table
      tx.executeSql(`
        CREATE TABLE IF NOT EXISTS order_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          order_id INTEGER NOT NULL,
          product_id INTEGER NOT NULL,
          product_name TEXT NOT NULL,
          price REAL NOT NULL,
          quantity INTEGER NOT NULL,
          total REAL NOT NULL,
          FOREIGN KEY (order_id) REFERENCES orders (id),
          FOREIGN KEY (product_id) REFERENCES products (id)
        );
      `);

      // Customers table
      tx.executeSql(`
        CREATE TABLE IF NOT EXISTS customers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          phone TEXT UNIQUE,
          email TEXT,
          address TEXT,
          total_orders INTEGER DEFAULT 0,
          total_spent REAL DEFAULT 0,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        );
      `);

      // Ingredients table
      tx.executeSql(`
        CREATE TABLE IF NOT EXISTS ingredients (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          unit TEXT NOT NULL,
          current_stock REAL DEFAULT 0,
          minimum_threshold REAL DEFAULT 0,
          price_per_unit REAL DEFAULT 0,
          supplier TEXT,
          is_active BOOLEAN DEFAULT 1,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        );
      `);

      // Recipe Ingredients table
      tx.executeSql(`
        CREATE TABLE IF NOT EXISTS recipe_ingredients (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          recipe_id INTEGER NOT NULL,
          ingredient_id INTEGER NOT NULL,
          quantity REAL NOT NULL,
          unit TEXT NOT NULL,
          notes TEXT,
          FOREIGN KEY (recipe_id) REFERENCES recipes (id),
          FOREIGN KEY (ingredient_id) REFERENCES ingredients (id)
        );
      `);

      // Production table
      tx.executeSql(`
        CREATE TABLE IF NOT EXISTS production (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          product_id INTEGER NOT NULL,
          recipe_id INTEGER,
          quantity_produced INTEGER NOT NULL,
          production_date TEXT NOT NULL,
          batch_number TEXT,
          notes TEXT,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (product_id) REFERENCES products (id),
          FOREIGN KEY (recipe_id) REFERENCES recipes (id)
        );
      `);

      // Staff table
      tx.executeSql(`
        CREATE TABLE IF NOT EXISTS staff (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          email TEXT UNIQUE NOT NULL,
          role TEXT NOT NULL,
          phone TEXT,
          is_active BOOLEAN DEFAULT 1,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        );
      `);

      // Settings table
      tx.executeSql(`
        CREATE TABLE IF NOT EXISTS settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          key TEXT UNIQUE NOT NULL,
          value TEXT NOT NULL,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        );
      `);

    }, reject, () => {
      console.log('Database initialized successfully');
      initializeSampleData().then(resolve).catch(reject);
    });
  });
};

// Initialize sample data (as per overview.txt)
const initializeSampleData = () => {
  return new Promise((resolve, reject) => {
    db.transaction(tx => {
      // Check if sample data already exists
      tx.executeSql(
        'SELECT COUNT(*) as count FROM products',
        [],
        (_, { rows }) => {
          if (rows._array[0].count === 0) {
            // Insert sample products (Indian bakery context)
            const sampleProducts = [
              {
                name: 'Chocolate Cake',
                description: 'Rich chocolate cake with cream frosting',
                category: ProductCategory.CAKES,
                selling_price: 450.0,
                cost_price: 280.0,
                servings: 8,
                is_popular: 1
              },
              {
                name: 'Vanilla Cupcakes',
                description: 'Soft vanilla cupcakes with buttercream',
                category: ProductCategory.CAKES,
                selling_price: 60.0,
                cost_price: 35.0,
                servings: 1,
                is_popular: 1
              },
              {
                name: 'Croissants',
                description: 'Buttery, flaky French pastries',
                category: ProductCategory.PASTRIES,
                selling_price: 80.0,
                cost_price: 45.0,
                servings: 1,
                is_popular: 1
              },
              {
                name: 'Sourdough Bread',
                description: 'Artisan sourdough bread loaf',
                category: ProductCategory.BREADS,
                selling_price: 120.0,
                cost_price: 70.0,
                servings: 12
              },
              {
                name: 'Blueberry Muffins',
                description: 'Fresh blueberry muffins',
                category: ProductCategory.PASTRIES,
                selling_price: 70.0,
                cost_price: 40.0,
                servings: 1
              },
              {
                name: 'Espresso',
                description: 'Strong Italian coffee',
                category: ProductCategory.BEVERAGES,
                selling_price: 45.0,
                cost_price: 15.0,
                servings: 1,
                is_popular: 1
              },
              {
                name: 'Cappuccino',
                description: 'Espresso with steamed milk foam',
                category: ProductCategory.BEVERAGES,
                selling_price: 65.0,
                cost_price: 25.0,
                servings: 1
              },
              {
                name: 'Red Velvet Cake',
                description: 'Classic red velvet with cream cheese frosting',
                category: ProductCategory.CAKES,
                selling_price: 520.0,
                cost_price: 320.0,
                servings: 8
              }
            ];

            sampleProducts.forEach(product => {
              tx.executeSql(
                `INSERT INTO products (name, description, category, selling_price, cost_price, servings, is_popular) 
                 VALUES (?, ?, ?, ?, ?, ?, ?)`,
                [product.name, product.description, product.category, product.selling_price, 
                 product.cost_price, product.servings, product.is_popular || 0]
              );
            });

            // Insert sample recipes
            const sampleRecipes = [
              {
                name: 'Chocolate Cake',
                instructions: 'Mix dry ingredients. Add wet ingredients. Bake at 180°C for 30 minutes.',
                servings: 8,
                prep_time: 20,
                cook_time: 30,
                difficulty: RecipeDifficulty.MEDIUM,
                category: ProductCategory.CAKES
              },
              {
                name: 'Vanilla Cupcakes',
                instructions: 'Cream butter and sugar. Add eggs and vanilla. Mix in flour. Bake 18 minutes.',
                servings: 12,
                prep_time: 15,
                cook_time: 18,
                difficulty: RecipeDifficulty.EASY,
                category: ProductCategory.CAKES
              },
              {
                name: 'Croissants',
                instructions: 'Prepare laminated dough. Shape and proof. Bake until golden brown.',
                servings: 8,
                prep_time: 180,
                cook_time: 20,
                difficulty: RecipeDifficulty.HARD,
                category: ProductCategory.PASTRIES
              }
            ];

            sampleRecipes.forEach(recipe => {
              tx.executeSql(
                `INSERT INTO recipes (name, instructions, servings, prep_time, cook_time, difficulty, category) 
                 VALUES (?, ?, ?, ?, ?, ?, ?)`,
                [recipe.name, recipe.instructions, recipe.servings, recipe.prep_time, 
                 recipe.cook_time, recipe.difficulty, recipe.category]
              );
            });

            console.log('Sample data inserted successfully');
          }
        }
      );
    }, reject, resolve);
  });
};

export { db };
