# Bakery Production Tracker Pro (Compose Edition)

A comprehensive native Android application for managing bakery production, inventory, and sales built with <PERSON><PERSON>in and Jetpack Compose.

## 🏗️ Architecture

This app follows Clean Architecture principles with MVVM pattern:

- **UI Layer**: Jetpack Compose with Material 3 design
- **Domain Layer**: Use cases and business logic
- **Data Layer**: Repository pattern with Room database
- **Dependency Injection**: Hilt
- **Navigation**: Navigation Compose with bottom navigation

## 🛠️ Tech Stack

- **Language**: Kotlin (JDK 17)
- **UI Framework**: Jetpack Compose (Material 3)
- **Architecture**: Clean MVVM + Repository + Use-Case layers
- **Dependency Injection**: Hilt
- **Local Database**: Room (SQLite)
- **Preferences**: DataStore (for theme and user preferences)
- **Navigation**: Navigation-Compose + BottomBar
- **PDF Generation**: iText7
- **CSV Operations**: OpenCSV
- **Camera**: CameraX + Storage Access Framework
- **Charts**: Custom Canvas-based charts
- **Background Tasks**: WorkManager
- **Testing**: JUnit5, Turbine, Compose UI Test, MockK
- **Code Quality**: ktlint, detekt, spotless-gradle

## 📱 Features

### Core Functionality

1. **Mock Authentication**
   - Login screen with role-based access (Admin/Staff)
   - Persistent session management with DataStore

2. **Dark Mode Support**
   - Toggle between light and dark themes
   - Preference persists across app restarts

3. **Dashboard**
   - Today's production overview with charts
   - Revenue tracking with weekly trends
   - Low stock alerts with quick restock actions
   - Recent activity feed

4. **Recipe Management**
   - CRUD operations for recipes
   - Multi-ingredient picker with quantities
   - Batch size configuration
   - Automatic product integration

5. **Dynamic Ingredient Scaling**
   - Automatic calculation of ingredient requirements
   - Batch quantity to per-unit conversion
   - Stock availability checking

6. **Production Tracking**
   - Record production with automatic stock updates
   - Undo functionality for corrections
   - Batch tracking and cost calculation
   - Staff member assignment

7. **Stock Management**
   - Ingredient inventory tracking
   - Low-stock threshold alerts
   - Price management
   - CSV import/export functionality

8. **Pricing Management** (Admin Only)
   - Real-time cost calculation based on ingredient prices
   - Manual cost override options
   - Profit margin analysis

9. **Bill Capture**
   - CameraX integration for bill photography
   - Image preview and ingredient mapping
   - Undo functionality for deletions

10. **Order Management**
    - Customer order creation
    - PDF invoice generation with QR codes
    - Discount and tax calculations
    - Share functionality

11. **Staff Management** (Admin Only)
    - Role-based access control
    - Permission management
    - Activity tracking

12. **Daily Backup & Alerts**
    - Automated nightly backups via WorkManager
    - CSV export to Documents/BakeryTracker/
    - Low-stock notifications

## 🎨 UI/UX Features

- **Material 3 Design**: Modern, accessible interface
- **Responsive Layout**: Optimized for various screen sizes
- **Custom Charts**: Production and revenue visualization
- **Intuitive Navigation**: Bottom navigation with role-based visibility
- **Interactive Components**: Cards, alerts, and action buttons
- **Preview Support**: All screens have Compose previews

## 🏃‍♂️ Getting Started

### Prerequisites

- Android Studio Hedgehog or later
- JDK 17
- Android SDK 24+ (Android 7.0)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd bakerytrack
```

2. Open the project in Android Studio

3. Sync the project with Gradle files

4. Run the app on an emulator or physical device

### Demo Credentials

- **Username**: demo
- **Password**: demo123
- **Roles**: Staff or Admin (selectable during login)

## 🧪 Testing

### Running Tests

```bash
# Unit tests
./gradlew testDebug

# Instrumented tests
./gradlew connectedAndroidTest

# All tests
./gradlew test
```

### Code Quality

```bash
# Lint check
./gradlew ktlintCheck

# Format code
./gradlew ktlintFormat

# Detekt analysis
./gradlew detekt

# All quality checks
./gradlew ktlintCheck detekt testDebug
```

## 📁 Project Structure

```
app/src/main/java/com/bakery/tracker/
├── data/
│   ├── local/
│   │   ├── dao/           # Room DAOs
│   │   └── BakeryDatabase.kt
│   ├── models/            # Data models
│   ├── preferences/       # DataStore preferences
│   └── repository/        # Repository implementations
├── di/                    # Hilt modules
├── domain/
│   └── usecase/          # Business logic use cases
├── ui/
│   ├── components/       # Reusable UI components
│   ├── navigation/       # Navigation setup
│   ├── screens/          # Screen composables
│   ├── theme/           # Theme and styling
│   └── viewmodel/       # ViewModels
├── util/                # Utility classes
├── worker/              # WorkManager workers
├── BakeryTrackerApp.kt  # Application class
└── MainActivity.kt      # Main activity
```

## 🔧 Configuration

### Database

The app uses Room database with the following entities:
- Ingredients
- Products
- Recipes
- RecipeIngredients
- ProductionLogs
- Bills
- BillItems

### Permissions

Required permissions:
- `CAMERA` - For bill capture
- `READ_EXTERNAL_STORAGE` - For file operations
- `WRITE_EXTERNAL_STORAGE` - For backup exports
- `INTERNET` - For future cloud features

## 🚀 Future Enhancements

- [ ] Cloud synchronization with Supabase/Firestore
- [ ] Real barcode scanning
- [ ] Advanced reporting and analytics
- [ ] Multi-language support
- [ ] Offline-first architecture
- [ ] Push notifications
- [ ] Advanced 3D product carousel
- [ ] Integration with POS systems

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Material Design 3 guidelines
- Jetpack Compose documentation
- Android Architecture Components
- Open source libraries used in this project

---

**Built with ❤️ using Jetpack Compose and Modern Android Development practices**
