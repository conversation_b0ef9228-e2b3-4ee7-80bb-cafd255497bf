import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import {
  Text,
  Button,
  TextInput,
  Card,
  Title,
  Divider,
  IconButton,
  Menu,
  TouchableRipple,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { 
  ProductCategory, 
  ProductCategoryDisplayNames,
  RecipeDifficulty,
  RecipeDifficultyDisplayNames,
  createRecipe 
} from '../types';

const AddRecipeModal = ({ onClose, onSave }) => {
  const [recipeName, setRecipeName] = useState('');
  const [instructions, setInstructions] = useState('');
  const [servings, setServings] = useState('1');
  const [prepTime, setPrepTime] = useState('0');
  const [cookTime, setCookTime] = useState('0');
  const [selectedCategory, setSelectedCategory] = useState(ProductCategory.CAKES);
  const [selectedDifficulty, setSelectedDifficulty] = useState(RecipeDifficulty.MEDIUM);
  const [showCategoryMenu, setShowCategoryMenu] = useState(false);
  const [showDifficultyMenu, setShowDifficultyMenu] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const { colors } = useTheme();

  const handleSave = async () => {
    if (!recipeName.trim()) {
      Alert.alert('Error', 'Please enter recipe name');
      return;
    }

    if (!instructions.trim()) {
      Alert.alert('Error', 'Please enter cooking instructions');
      return;
    }

    const servingsNum = parseInt(servings) || 1;
    const prepTimeNum = parseInt(prepTime) || 0;
    const cookTimeNum = parseInt(cookTime) || 0;

    if (servingsNum < 1) {
      Alert.alert('Error', 'Servings must be at least 1');
      return;
    }

    setIsSaving(true);
    try {
      const recipe = createRecipe({
        name: recipeName.trim(),
        instructions: instructions.trim(),
        servings: servingsNum,
        prepTime: prepTimeNum,
        cookTime: cookTimeNum,
        difficulty: selectedDifficulty,
        category: selectedCategory
      });

      await onSave(recipe);
    } catch (error) {
      console.error('Error saving recipe:', error);
      Alert.alert('Error', 'Failed to save recipe');
    } finally {
      setIsSaving(false);
    }
  };

  const getTotalTime = () => {
    const prep = parseInt(prepTime) || 0;
    const cook = parseInt(cookTime) || 0;
    return prep + cook;
  };

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Title style={[styles.title, { color: colors.onSurface }]}>
          Add New Recipe
        </Title>
        <IconButton
          icon="close"
          size={24}
          onPress={onClose}
        />
      </View>

      <Divider />

      {/* Basic Information */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.onSurface }]}>
          Basic Information
        </Text>

        <TextInput
          label="Recipe Name *"
          value={recipeName}
          onChangeText={setRecipeName}
          mode="outlined"
          style={styles.input}
          disabled={isSaving}
        />

        <View style={styles.row}>
          {/* Category Dropdown */}
          <View style={styles.halfWidth}>
            <Menu
              visible={showCategoryMenu}
              onDismiss={() => setShowCategoryMenu(false)}
              anchor={
                <TouchableRipple
                  onPress={() => setShowCategoryMenu(true)}
                  style={[styles.dropdown, { borderColor: colors.outline }]}
                  disabled={isSaving}
                >
                  <View style={styles.dropdownContent}>
                    <Text style={[styles.dropdownText, { color: colors.onSurface }]}>
                      {ProductCategoryDisplayNames[selectedCategory]}
                    </Text>
                    <MaterialCommunityIcons
                      name="chevron-down"
                      size={20}
                      color={colors.onSurfaceVariant}
                    />
                  </View>
                </TouchableRipple>
              }
            >
              {Object.values(ProductCategory).map(category => (
                <Menu.Item
                  key={category}
                  onPress={() => {
                    setSelectedCategory(category);
                    setShowCategoryMenu(false);
                  }}
                  title={ProductCategoryDisplayNames[category]}
                />
              ))}
            </Menu>
            <Text style={[styles.dropdownLabel, { color: colors.onSurfaceVariant }]}>
              Category
            </Text>
          </View>

          {/* Difficulty Dropdown */}
          <View style={styles.halfWidth}>
            <Menu
              visible={showDifficultyMenu}
              onDismiss={() => setShowDifficultyMenu(false)}
              anchor={
                <TouchableRipple
                  onPress={() => setShowDifficultyMenu(true)}
                  style={[styles.dropdown, { borderColor: colors.outline }]}
                  disabled={isSaving}
                >
                  <View style={styles.dropdownContent}>
                    <Text style={[styles.dropdownText, { color: colors.onSurface }]}>
                      {RecipeDifficultyDisplayNames[selectedDifficulty]}
                    </Text>
                    <MaterialCommunityIcons
                      name="chevron-down"
                      size={20}
                      color={colors.onSurfaceVariant}
                    />
                  </View>
                </TouchableRipple>
              }
            >
              {Object.values(RecipeDifficulty).map(difficulty => (
                <Menu.Item
                  key={difficulty}
                  onPress={() => {
                    setSelectedDifficulty(difficulty);
                    setShowDifficultyMenu(false);
                  }}
                  title={RecipeDifficultyDisplayNames[difficulty]}
                />
              ))}
            </Menu>
            <Text style={[styles.dropdownLabel, { color: colors.onSurfaceVariant }]}>
              Difficulty
            </Text>
          </View>
        </View>

        <View style={styles.row}>
          <TextInput
            label="Servings"
            value={servings}
            onChangeText={setServings}
            mode="outlined"
            keyboardType="numeric"
            style={styles.thirdWidth}
            disabled={isSaving}
          />

          <TextInput
            label="Prep Time (min)"
            value={prepTime}
            onChangeText={setPrepTime}
            mode="outlined"
            keyboardType="numeric"
            style={styles.thirdWidth}
            disabled={isSaving}
          />

          <TextInput
            label="Cook Time (min)"
            value={cookTime}
            onChangeText={setCookTime}
            mode="outlined"
            keyboardType="numeric"
            style={styles.thirdWidth}
            disabled={isSaving}
          />
        </View>

        {/* Total Time Display */}
        <Card style={[styles.timeCard, { backgroundColor: colors.surfaceVariant }]}>
          <Card.Content style={styles.timeContent}>
            <MaterialCommunityIcons
              name="clock-outline"
              size={20}
              color={colors.primary}
            />
            <Text style={[styles.timeText, { color: colors.onSurfaceVariant }]}>
              Total Time: {getTotalTime()} minutes
            </Text>
          </Card.Content>
        </Card>

        <TextInput
          label="Cooking Instructions *"
          value={instructions}
          onChangeText={setInstructions}
          mode="outlined"
          multiline
          numberOfLines={4}
          style={styles.textArea}
          disabled={isSaving}
        />
      </View>

      {/* Future: Ingredients Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.onSurface }]}>
          Ingredients
        </Text>
        <Card style={styles.comingSoonCard}>
          <Card.Content style={styles.comingSoonContent}>
            <MaterialCommunityIcons
              name="plus-circle-outline"
              size={40}
              color={colors.onSurfaceVariant}
            />
            <Text style={[styles.comingSoonText, { color: colors.onSurfaceVariant }]}>
              Ingredient management coming soon
            </Text>
          </Card.Content>
        </Card>
      </View>

      {/* Actions */}
      <View style={styles.actions}>
        <Button
          mode="outlined"
          onPress={onClose}
          style={styles.actionButton}
          disabled={isSaving}
        >
          Cancel
        </Button>
        <Button
          mode="contained"
          onPress={handleSave}
          style={styles.actionButton}
          loading={isSaving}
          disabled={isSaving}
        >
          {isSaving ? 'Saving...' : 'Save Recipe'}
        </Button>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    maxHeight: '90%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  section: {
    padding: 16,
    paddingTop: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  input: {
    marginBottom: 12,
  },
  textArea: {
    marginBottom: 12,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  halfWidth: {
    width: '48%',
  },
  thirdWidth: {
    width: '31%',
  },
  dropdown: {
    borderWidth: 1,
    borderRadius: 4,
    padding: 12,
    minHeight: 56,
    justifyContent: 'center',
  },
  dropdownContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dropdownText: {
    fontSize: 16,
  },
  dropdownLabel: {
    fontSize: 12,
    marginTop: 4,
  },
  timeCard: {
    marginBottom: 12,
  },
  timeContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  timeText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  comingSoonCard: {
    marginBottom: 12,
  },
  comingSoonContent: {
    alignItems: 'center',
    padding: 20,
  },
  comingSoonText: {
    marginTop: 8,
    fontSize: 14,
    textAlign: 'center',
  },
  actions: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});

export default AddRecipeModal;
