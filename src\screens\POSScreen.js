import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  FlatList,
  Alert,
  Dimensions,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  Chip,
  Searchbar,
  FAB,
  Portal,
  Modal,
  Title,
  Paragraph,
  Divider,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useDatabase } from '../context/DatabaseContext';
import { useCart } from '../context/CartContext';
import { ProductCategory, ProductCategoryDisplayNames } from '../types';
import ProductCard from '../components/ProductCard';
import CartSummary from '../components/CartSummary';
import CheckoutModal from '../components/CheckoutModal';

const { width } = Dimensions.get('window');
const isTablet = width > 768;

const POSScreen = () => {
  const [products, setProducts] = useState([]);
  const [popularProducts, setPopularProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [showCart, setShowCart] = useState(false);
  const [showCheckout, setShowCheckout] = useState(false);

  const { colors } = useTheme();
  const { getProducts, getPopularProducts } = useDatabase();
  const { cartItems, cartSummary, addToCart } = useCart();

  useEffect(() => {
    loadProducts();
    loadPopularProducts();
  }, []);

  useEffect(() => {
    filterProducts();
  }, [products, selectedCategory, searchQuery]);

  const loadProducts = async () => {
    try {
      const productsData = await getProducts();
      setProducts(productsData);
    } catch (error) {
      console.error('Error loading products:', error);
      Alert.alert('Error', 'Failed to load products');
    } finally {
      setIsLoading(false);
    }
  };

  const loadPopularProducts = async () => {
    try {
      const popularData = await getPopularProducts();
      setPopularProducts(popularData);
    } catch (error) {
      console.error('Error loading popular products:', error);
    }
  };

  const filterProducts = () => {
    let filtered = products;

    if (selectedCategory) {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredProducts(filtered);
  };

  const handleAddToCart = async (product) => {
    try {
      await addToCart(product);
      // Show success feedback
    } catch (error) {
      Alert.alert('Error', 'Failed to add item to cart');
    }
  };

  const handleCategorySelect = (category) => {
    setSelectedCategory(selectedCategory === category ? null : category);
  };

  const handleCheckout = () => {
    if (cartItems.length === 0) {
      Alert.alert('Empty Cart', 'Please add items to cart before checkout');
      return;
    }
    setShowCheckout(true);
  };

  const renderCategoryChips = () => (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.categoryContainer}
      contentContainerStyle={styles.categoryContent}
    >
      <Chip
        mode={selectedCategory === null ? 'flat' : 'outlined'}
        selected={selectedCategory === null}
        onPress={() => handleCategorySelect(null)}
        style={styles.categoryChip}
      >
        All
      </Chip>
      {Object.values(ProductCategory).map(category => (
        <Chip
          key={category}
          mode={selectedCategory === category ? 'flat' : 'outlined'}
          selected={selectedCategory === category}
          onPress={() => handleCategorySelect(category)}
          style={styles.categoryChip}
        >
          {ProductCategoryDisplayNames[category]}
        </Chip>
      ))}
    </ScrollView>
  );

  const renderPopularSection = () => {
    if (popularProducts.length === 0) return null;

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.onBackground }]}>
          Popular Items
        </Text>
        <FlatList
          data={popularProducts}
          renderItem={({ item }) => (
            <ProductCard
              product={item}
              onAddToCart={() => handleAddToCart(item)}
              style={styles.popularCard}
            />
          )}
          keyExtractor={item => item.id.toString()}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.popularList}
        />
      </View>
    );
  };

  const renderProductGrid = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.onBackground }]}>
        All Products
      </Text>
      <FlatList
        data={filteredProducts}
        renderItem={({ item }) => (
          <ProductCard
            product={item}
            onAddToCart={() => handleAddToCart(item)}
            style={styles.productCard}
          />
        )}
        keyExtractor={item => item.id.toString()}
        numColumns={isTablet ? 3 : 2}
        contentContainerStyle={styles.productGrid}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );

  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <MaterialCommunityIcons name="loading" size={50} color={colors.primary} />
        <Text style={{ color: colors.onBackground, marginTop: 10 }}>
          Loading products...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <Text style={[styles.headerTitle, { color: colors.onSurface }]}>
          Point of Sale
        </Text>
        <Searchbar
          placeholder="Search products..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
          inputStyle={{ fontSize: 14 }}
        />
      </View>

      {/* Category Filters */}
      {renderCategoryChips()}

      {/* Main Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderPopularSection()}
        {renderProductGrid()}
      </ScrollView>

      {/* Cart Summary FAB */}
      {cartItems.length > 0 && (
        <FAB
          icon="cart"
          label={`₹${cartSummary.subtotal.toFixed(2)} (${cartSummary.itemCount})`}
          style={[styles.cartFab, { backgroundColor: colors.primary }]}
          onPress={() => setShowCart(true)}
        />
      )}

      {/* Cart Modal */}
      <Portal>
        <Modal
          visible={showCart}
          onDismiss={() => setShowCart(false)}
          contentContainerStyle={[styles.modal, { backgroundColor: colors.surface }]}
        >
          <CartSummary
            onClose={() => setShowCart(false)}
            onCheckout={handleCheckout}
          />
        </Modal>
      </Portal>

      {/* Checkout Modal */}
      <Portal>
        <Modal
          visible={showCheckout}
          onDismiss={() => setShowCheckout(false)}
          contentContainerStyle={[styles.modal, { backgroundColor: colors.surface }]}
        >
          <CheckoutModal
            onClose={() => setShowCheckout(false)}
            onSuccess={() => {
              setShowCheckout(false);
              setShowCart(false);
            }}
          />
        </Modal>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 16,
    elevation: 2,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  searchbar: {
    elevation: 0,
    borderRadius: 8,
  },
  categoryContainer: {
    maxHeight: 60,
  },
  categoryContent: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  categoryChip: {
    marginRight: 8,
  },
  content: {
    flex: 1,
  },
  section: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  popularList: {
    paddingRight: 16,
  },
  popularCard: {
    width: 200,
    marginRight: 12,
  },
  productGrid: {
    paddingBottom: 100,
  },
  productCard: {
    flex: 1,
    margin: 6,
    maxWidth: isTablet ? '31%' : '47%',
  },
  cartFab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  modal: {
    margin: 20,
    borderRadius: 12,
    maxHeight: '80%',
  },
});

export default POSScreen;
