<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.BakeryTracker" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Customize your light theme here. -->
        <item name="colorPrimary">@color/bakery_primary</item>
        <item name="colorOnPrimary">@color/bakery_on_primary</item>
        <item name="colorPrimaryContainer">@color/bakery_secondary</item>
        <item name="colorOnPrimaryContainer">@color/bakery_on_secondary</item>
        <item name="colorSecondary">@color/bakery_secondary</item>
        <item name="colorOnSecondary">@color/bakery_on_secondary</item>
        <item name="colorSecondaryContainer">@color/bakery_secondary_variant</item>
        <item name="colorOnSecondaryContainer">@color/bakery_on_secondary</item>
        <item name="colorTertiary">@color/low_stock_warning</item>
        <item name="colorOnTertiary">@color/bakery_on_primary</item>
        <item name="colorError">@color/bakery_error</item>
        <item name="colorOnError">@color/bakery_on_error</item>
        <item name="colorBackground">@color/bakery_background</item>
        <item name="colorOnBackground">@color/bakery_on_background</item>
        <item name="colorSurface">@color/bakery_surface</item>
        <item name="colorOnSurface">@color/bakery_on_surface</item>
        <item name="android:statusBarColor">?attr/colorPrimary</item>
    </style>
</resources>
