import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Dimensions,
} from 'react-native';
import {
  Text,
  Card,
  Title,
  Paragraph,
  Button,
  Chip,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart } from 'react-native-chart-kit';
import { useTheme } from '../context/ThemeContext';
import { useDatabase } from '../context/DatabaseContext';
import { useAuth } from '../context/AuthContext';

const { width } = Dimensions.get('window');
const chartWidth = width - 40;

const DashboardScreen = () => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [dailySales, setDailySales] = useState(0);
  const [todayOrders, setTodayOrders] = useState([]);
  const [weeklySales, setWeeklySales] = useState([]);
  const [topProducts, setTopProducts] = useState([]);
  const [todayProduction, setTodayProduction] = useState([]);

  const { colors } = useTheme();
  const { user } = useAuth();
  const {
    getDailySales,
    getTodayOrders,
    getWeeklySales,
    getTopSellingProducts,
    getTodayProduction,
  } = useDatabase();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      const [sales, orders, weekly, products, production] = await Promise.all([
        getDailySales(),
        getTodayOrders(),
        getWeeklySales(),
        getTopSellingProducts(),
        getTodayProduction(),
      ]);

      setDailySales(sales);
      setTodayOrders(orders);
      setWeeklySales(weekly);
      setTopProducts(products);
      setTodayProduction(production);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  };

  const onRefresh = async () => {
    setIsRefreshing(true);
    await loadDashboardData();
    setIsRefreshing(false);
  };

  const formatPrice = (price) => {
    return `₹${price.toFixed(2)}`;
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  // Mock data for demo (as per overview.txt)
  const mockQuickStats = [
    {
      title: "Today's Sales",
      value: formatPrice(dailySales || 12450),
      subtitle: 'revenue',
      icon: 'currency-inr',
      color: colors.success,
    },
    {
      title: 'Orders',
      value: todayOrders.length || 47,
      subtitle: 'completed',
      icon: 'clipboard-check',
      color: colors.primary,
    },
    {
      title: 'Production',
      value: todayProduction.reduce((sum, item) => sum + (item.total_produced || 0), 0) || 156,
      subtitle: 'items made',
      icon: 'factory',
      color: colors.info,
    },
    {
      title: 'Low Stock',
      value: 8,
      subtitle: 'ingredients',
      icon: 'alert-circle',
      color: colors.warning,
    },
  ];

  const mockRecentActivities = [
    {
      title: 'Order BT-********-1234 completed - ₹850',
      time: '5 minutes ago',
      icon: 'check-circle',
      color: colors.success,
    },
    {
      title: '12 Chocolate Cakes produced',
      time: '15 minutes ago',
      icon: 'factory',
      color: colors.info,
    },
    {
      title: 'Low stock alert: Vanilla Extract',
      time: '30 minutes ago',
      icon: 'alert-circle',
      color: colors.warning,
    },
    {
      title: 'New customer added: John Doe',
      time: '1 hour ago',
      icon: 'account-plus',
      color: colors.primary,
    },
    {
      title: 'Recipe updated: Blueberry Muffins',
      time: '2 hours ago',
      icon: 'book-edit',
      color: colors.secondary,
    },
  ];

  // Chart data
  const weeklyChartData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        data: [8500, 9200, 11000, 9800, 12500, 14000, 11500],
        color: (opacity = 1) => `rgba(103, 80, 164, ${opacity})`,
        strokeWidth: 3,
      },
    ],
  };

  const productionChartData = {
    labels: ['Cakes', 'Pastries', 'Breads', 'Beverages'],
    datasets: [
      {
        data: [35, 28, 42, 18],
      },
    ],
  };

  const renderQuickStats = () => (
    <View style={styles.section}>
      <Title style={[styles.sectionTitle, { color: colors.onBackground }]}>
        Quick Stats
      </Title>
      <View style={styles.statsGrid}>
        {mockQuickStats.map((stat, index) => (
          <Card key={index} style={styles.statCard}>
            <Card.Content style={styles.statContent}>
              <View style={styles.statHeader}>
                <MaterialCommunityIcons
                  name={stat.icon}
                  size={24}
                  color={stat.color}
                />
                <Text style={[styles.statValue, { color: colors.onSurface }]}>
                  {stat.value}
                </Text>
              </View>
              <Text style={[styles.statTitle, { color: colors.onSurfaceVariant }]}>
                {stat.title}
              </Text>
              <Text style={[styles.statSubtitle, { color: colors.onSurfaceVariant }]}>
                {stat.subtitle}
              </Text>
            </Card.Content>
          </Card>
        ))}
      </View>
    </View>
  );

  const renderWeeklyChart = () => (
    <View style={styles.section}>
      <Title style={[styles.sectionTitle, { color: colors.onBackground }]}>
        Weekly Revenue
      </Title>
      <Card style={styles.chartCard}>
        <Card.Content>
          <LineChart
            data={weeklyChartData}
            width={chartWidth - 32}
            height={200}
            chartConfig={{
              backgroundColor: colors.surface,
              backgroundGradientFrom: colors.surface,
              backgroundGradientTo: colors.surface,
              decimalPlaces: 0,
              color: (opacity = 1) => colors.primary,
              labelColor: (opacity = 1) => colors.onSurface,
              style: {
                borderRadius: 16,
              },
              propsForDots: {
                r: '4',
                strokeWidth: '2',
                stroke: colors.primary,
              },
            }}
            bezier
            style={styles.chart}
          />
        </Card.Content>
      </Card>
    </View>
  );

  const renderRecentActivities = () => (
    <View style={styles.section}>
      <Title style={[styles.sectionTitle, { color: colors.onBackground }]}>
        Recent Activities
      </Title>
      {mockRecentActivities.map((activity, index) => (
        <Card key={index} style={styles.activityCard}>
          <Card.Content style={styles.activityContent}>
            <MaterialCommunityIcons
              name={activity.icon}
              size={20}
              color={activity.color}
              style={styles.activityIcon}
            />
            <View style={styles.activityText}>
              <Text style={[styles.activityTitle, { color: colors.onSurface }]}>
                {activity.title}
              </Text>
              <Text style={[styles.activityTime, { color: colors.onSurfaceVariant }]}>
                {activity.time}
              </Text>
            </View>
          </Card.Content>
        </Card>
      ))}
    </View>
  );

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: colors.background }]}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={[styles.greeting, { color: colors.onBackground }]}>
            {getGreeting()}, {user?.name}!
          </Text>
          <Text style={[styles.date, { color: colors.onSurfaceVariant }]}>
            {new Date().toLocaleDateString('en-IN', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })}
          </Text>
        </View>
        <Chip mode="outlined" icon="account-circle">
          {user?.role}
        </Chip>
      </View>

      {renderQuickStats()}
      {renderWeeklyChart()}
      {renderRecentActivities()}

      <View style={styles.bottomPadding} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 10,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  date: {
    fontSize: 14,
    marginTop: 4,
  },
  section: {
    padding: 20,
    paddingTop: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: '48%',
    marginBottom: 12,
    elevation: 2,
  },
  statContent: {
    padding: 12,
  },
  statHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  statTitle: {
    fontSize: 12,
    fontWeight: '600',
  },
  statSubtitle: {
    fontSize: 10,
    marginTop: 2,
  },
  chartCard: {
    elevation: 2,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  activityCard: {
    marginBottom: 8,
    elevation: 1,
  },
  activityContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  activityIcon: {
    marginRight: 12,
  },
  activityText: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  activityTime: {
    fontSize: 12,
  },
  bottomPadding: {
    height: 20,
  },
});

export default DashboardScreen;
