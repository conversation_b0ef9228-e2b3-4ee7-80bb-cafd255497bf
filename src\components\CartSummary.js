import React from 'react';
import { View, StyleSheet, FlatList } from 'react-native';
import {
  Text,
  Button,
  IconButton,
  Divider,
  Title,
  Card,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useCart } from '../context/CartContext';
import CartItemCard from './CartItemCard';

const CartSummary = ({ onClose, onCheckout }) => {
  const { colors } = useTheme();
  const { cartItems, cartSummary, clearCart } = useCart();

  const handleClearCart = () => {
    clearCart();
  };

  const formatPrice = (price) => {
    return `₹${price.toFixed(2)}`;
  };

  if (cartItems.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Title style={[styles.title, { color: colors.onSurface }]}>
            Shopping Cart
          </Title>
          <IconButton
            icon="close"
            size={24}
            onPress={onClose}
          />
        </View>

        <View style={styles.emptyContainer}>
          <MaterialCommunityIcons
            name="cart-outline"
            size={80}
            color={colors.onSurfaceVariant}
          />
          <Text style={[styles.emptyText, { color: colors.onSurfaceVariant }]}>
            Your cart is empty
          </Text>
          <Text style={[styles.emptySubtext, { color: colors.onSurfaceVariant }]}>
            Add products to get started
          </Text>
        </View>

        <Button
          mode="outlined"
          onPress={onClose}
          style={styles.closeButton}
        >
          Continue Shopping
        </Button>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Title style={[styles.title, { color: colors.onSurface }]}>
          Shopping Cart
        </Title>
        <View style={styles.headerActions}>
          <Button
            mode="text"
            onPress={handleClearCart}
            textColor={colors.error}
            compact
          >
            Clear All
          </Button>
          <IconButton
            icon="close"
            size={24}
            onPress={onClose}
          />
        </View>
      </View>

      <Divider />

      {/* Cart Items */}
      <FlatList
        data={cartItems}
        renderItem={({ item }) => (
          <CartItemCard cartItem={item} />
        )}
        keyExtractor={item => item.id.toString()}
        style={styles.itemsList}
        showsVerticalScrollIndicator={false}
      />

      <Divider />

      {/* Summary */}
      <Card style={[styles.summaryCard, { backgroundColor: colors.surfaceVariant }]}>
        <Card.Content style={styles.summaryContent}>
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: colors.onSurfaceVariant }]}>
              Items:
            </Text>
            <Text style={[styles.summaryValue, { color: colors.onSurfaceVariant }]}>
              {cartSummary.itemCount}
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: colors.onSurfaceVariant }]}>
              Products:
            </Text>
            <Text style={[styles.summaryValue, { color: colors.onSurfaceVariant }]}>
              {cartSummary.uniqueProductCount}
            </Text>
          </View>

          <Divider style={styles.summaryDivider} />

          <View style={styles.summaryRow}>
            <Text style={[styles.totalLabel, { color: colors.onSurface }]}>
              Total:
            </Text>
            <Text style={[styles.totalValue, { color: colors.primary }]}>
              {formatPrice(cartSummary.subtotal)}
            </Text>
          </View>
        </Card.Content>
      </Card>

      {/* Actions */}
      <View style={styles.actions}>
        <Button
          mode="outlined"
          onPress={onClose}
          style={styles.actionButton}
        >
          Continue Shopping
        </Button>
        <Button
          mode="contained"
          onPress={onCheckout}
          style={styles.actionButton}
          icon="credit-card"
        >
          Checkout
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  closeButton: {
    margin: 16,
  },
  itemsList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  summaryCard: {
    margin: 16,
    marginBottom: 8,
  },
  summaryContent: {
    padding: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  summaryDivider: {
    marginVertical: 8,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  totalValue: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  actions: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});

export default CartSummary;
