package com.bakery.tracker.data.models

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Represents an ingredient used in bakery production.
 * 
 * @param id Unique identifier for the ingredient
 * @param name Name of the ingredient (e.g., "Flour", "Sugar")
 * @param unit Unit of measurement (e.g., "kg", "liters", "pieces")
 * @param currentStock Current quantity in stock
 * @param lowStockThreshold Minimum quantity before low stock alert
 * @param lastPaidPrice Last price paid per unit
 * @param supplier Supplier name or information
 * @param createdAt Timestamp when ingredient was added
 * @param updatedAt Timestamp when ingredient was last updated
 */
@Entity(tableName = "ingredients")
data class Ingredient(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val unit: String,
    val currentStock: Double,
    val lowStockThreshold: Double,
    val lastPaidPrice: Double,
    val supplier: String = "",
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    /**
     * Checks if the ingredient is below the low stock threshold
     */
    fun isLowStock(): Boolean = currentStock <= lowStockThreshold
    
    /**
     * Calculates the total value of current stock
     */
    fun getTotalStockValue(): Double = currentStock * lastPaidPrice
}
