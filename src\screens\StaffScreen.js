import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card, Button } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';

const StaffScreen = () => {
  const { colors } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <MaterialCommunityIcons
          name="account-group"
          size={80}
          color={colors.primary}
        />
        <Text style={[styles.title, { color: colors.onBackground }]}>
          Staff Management
        </Text>
        <Text style={[styles.subtitle, { color: colors.onSurfaceVariant }]}>
          Manage staff accounts, roles, and permissions
        </Text>
        
        <Card style={styles.featureCard}>
          <Card.Content>
            <Text style={[styles.featureTitle, { color: colors.onSurface }]}>
              Staff Features:
            </Text>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • Add/remove staff members
            </Text>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • Role-based access control (Admin/Staff)
            </Text>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • Staff performance tracking
            </Text>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • Shift scheduling
            </Text>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • Activity logs and reports
            </Text>
          </Card.Content>
        </Card>

        <Button mode="contained" onPress={() => {}}>
          Manage Staff
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  content: {
    alignItems: 'center',
    maxWidth: 400,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
  },
  featureCard: {
    width: '100%',
    marginBottom: 30,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  featureItem: {
    fontSize: 14,
    marginBottom: 5,
  },
});

export default StaffScreen;
