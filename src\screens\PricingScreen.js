import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card, Button } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';

const PricingScreen = () => {
  const { colors } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <MaterialCommunityIcons
          name="currency-inr"
          size={80}
          color={colors.primary}
        />
        <Text style={[styles.title, { color: colors.onBackground }]}>
          Pricing Management
        </Text>
        <Text style={[styles.subtitle, { color: colors.onSurfaceVariant }]}>
          Set product prices, calculate margins, and manage cost analysis
        </Text>
        
        <Card style={styles.featureCard}>
          <Card.Content>
            <Text style={[styles.featureTitle, { color: colors.onSurface }]}>
              Pricing Features:
            </Text>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • Profit margin calculation: ((selling - cost) / selling) × 100
            </Text>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • Suggested pricing (30%, 40%, 50% margins)
            </Text>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • Cost per serving analysis
            </Text>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • Competitive pricing insights
            </Text>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • Price history tracking
            </Text>
          </Card.Content>
        </Card>

        <Button mode="contained" onPress={() => {}}>
          Manage Pricing
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  content: {
    alignItems: 'center',
    maxWidth: 400,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
  },
  featureCard: {
    width: '100%',
    marginBottom: 30,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  featureItem: {
    fontSize: 14,
    marginBottom: 5,
    lineHeight: 20,
  },
});

export default PricingScreen;
