import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, Text, Button, Chip, IconButton } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { ProductCategoryDisplayNames, RecipeDifficultyDisplayNames } from '../types';

const RecipeCard = ({ recipe, onDelete }) => {
  const { colors } = useTheme();

  const formatTime = (minutes) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
  };

  const getTotalTime = () => {
    return (recipe.prep_time || recipe.prepTime || 0) + (recipe.cook_time || recipe.cookTime || 0);
  };

  return (
    <Card style={styles.card}>
      <Card.Content style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Text style={[styles.title, { color: colors.onSurface }]} numberOfLines={2}>
              {recipe.name}
            </Text>
            <IconButton
              icon="delete"
              size={20}
              onPress={onDelete}
              iconColor={colors.error}
              style={styles.deleteButton}
            />
          </View>
        </View>

        {/* Category and Difficulty */}
        <View style={styles.chipContainer}>
          <Chip
            mode="outlined"
            compact
            style={styles.chip}
            textStyle={styles.chipText}
          >
            {ProductCategoryDisplayNames[recipe.category]}
          </Chip>
          <Chip
            mode="outlined"
            compact
            style={styles.chip}
            textStyle={styles.chipText}
          >
            {RecipeDifficultyDisplayNames[recipe.difficulty]}
          </Chip>
        </View>

        {/* Recipe Details */}
        <View style={styles.detailsContainer}>
          <View style={styles.detailItem}>
            <MaterialCommunityIcons
              name="account-group"
              size={16}
              color={colors.onSurfaceVariant}
            />
            <Text style={[styles.detailText, { color: colors.onSurfaceVariant }]}>
              {recipe.servings} servings
            </Text>
          </View>

          <View style={styles.detailItem}>
            <MaterialCommunityIcons
              name="clock-outline"
              size={16}
              color={colors.onSurfaceVariant}
            />
            <Text style={[styles.detailText, { color: colors.onSurfaceVariant }]}>
              Prep: {formatTime(recipe.prep_time || recipe.prepTime || 0)}
            </Text>
          </View>

          <View style={styles.detailItem}>
            <MaterialCommunityIcons
              name="fire"
              size={16}
              color={colors.onSurfaceVariant}
            />
            <Text style={[styles.detailText, { color: colors.onSurfaceVariant }]}>
              Cook: {formatTime(recipe.cook_time || recipe.cookTime || 0)}
            </Text>
          </View>

          <View style={styles.detailItem}>
            <MaterialCommunityIcons
              name="timer"
              size={16}
              color={colors.primary}
            />
            <Text style={[styles.detailText, { color: colors.primary }]}>
              Total: {formatTime(getTotalTime())}
            </Text>
          </View>
        </View>

        {/* Instructions Preview */}
        {recipe.instructions && (
          <View style={styles.instructionsContainer}>
            <Text style={[styles.instructionsLabel, { color: colors.onSurfaceVariant }]}>
              Instructions:
            </Text>
            <Text
              style={[styles.instructions, { color: colors.onSurface }]}
              numberOfLines={3}
            >
              {recipe.instructions}
            </Text>
          </View>
        )}

        {/* Actions */}
        <View style={styles.actions}>
          <Button
            mode="outlined"
            onPress={() => {}}
            style={styles.actionButton}
            compact
          >
            View Details
          </Button>
          <Button
            mode="contained"
            onPress={() => {}}
            style={styles.actionButton}
            compact
          >
            Use Recipe
          </Button>
        </View>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 12,
    elevation: 2,
  },
  content: {
    padding: 16,
  },
  header: {
    marginBottom: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 8,
  },
  deleteButton: {
    margin: 0,
    width: 32,
    height: 32,
  },
  chipContainer: {
    flexDirection: 'row',
    marginBottom: 12,
    flexWrap: 'wrap',
  },
  chip: {
    marginRight: 8,
    marginBottom: 4,
    height: 24,
  },
  chipText: {
    fontSize: 10,
  },
  detailsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 4,
  },
  detailText: {
    fontSize: 12,
    marginLeft: 4,
  },
  instructionsContainer: {
    marginBottom: 12,
  },
  instructionsLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
  },
  instructions: {
    fontSize: 14,
    lineHeight: 20,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
  },
  actionButton: {
    minWidth: 80,
  },
});

export default RecipeCard;
