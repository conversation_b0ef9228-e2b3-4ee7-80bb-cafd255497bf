import React from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import {
  Text,
  Card,
  Button,
  List,
  Switch,
  Divider,
  Title,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';

const SettingsScreen = () => {
  const { colors, isDarkMode, toggleTheme, themeMode, setSystemTheme, setLightTheme, setDarkTheme } = useTheme();
  const { user, logout, switchRole, isAdmin } = useAuth();

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', style: 'destructive', onPress: logout },
      ]
    );
  };

  const handleRoleSwitch = () => {
    const newRole = user.role === 'ADMIN' ? 'STAFF' : 'ADMIN';
    Alert.alert(
      'Switch Role',
      `Switch to ${newRole} role?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Switch', onPress: () => switchRole(newRole) },
      ]
    );
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* User Info */}
      <Card style={styles.card}>
        <Card.Content>
          <View style={styles.userInfo}>
            <MaterialCommunityIcons
              name="account-circle"
              size={60}
              color={colors.primary}
            />
            <View style={styles.userDetails}>
              <Text style={[styles.userName, { color: colors.onSurface }]}>
                {user?.name}
              </Text>
              <Text style={[styles.userEmail, { color: colors.onSurfaceVariant }]}>
                {user?.email}
              </Text>
              <Text style={[styles.userRole, { color: colors.primary }]}>
                {user?.role} User
              </Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      {/* Theme Settings */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={[styles.sectionTitle, { color: colors.onSurface }]}>
            Appearance
          </Title>
          
          <List.Item
            title="Dark Mode"
            description="Toggle dark/light theme"
            left={props => <List.Icon {...props} icon="theme-light-dark" />}
            right={() => (
              <Switch
                value={isDarkMode}
                onValueChange={toggleTheme}
              />
            )}
          />
          
          <Divider />
          
          <List.Item
            title="Theme Mode"
            description={`Current: ${themeMode}`}
            left={props => <List.Icon {...props} icon="palette" />}
            onPress={() => {
              Alert.alert(
                'Theme Mode',
                'Choose theme preference',
                [
                  { text: 'System', onPress: setSystemTheme },
                  { text: 'Light', onPress: setLightTheme },
                  { text: 'Dark', onPress: setDarkTheme },
                  { text: 'Cancel', style: 'cancel' },
                ]
              );
            }}
          />
        </Card.Content>
      </Card>

      {/* Account Settings */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={[styles.sectionTitle, { color: colors.onSurface }]}>
            Account
          </Title>
          
          <List.Item
            title="Switch Role"
            description={`Currently: ${user?.role}`}
            left={props => <List.Icon {...props} icon="account-switch" />}
            onPress={handleRoleSwitch}
          />
          
          <Divider />
          
          <List.Item
            title="Change Password"
            description="Update your password"
            left={props => <List.Icon {...props} icon="lock-reset" />}
            onPress={() => Alert.alert('Info', 'Password change feature coming soon')}
          />
          
          <Divider />
          
          <List.Item
            title="Profile Settings"
            description="Update your profile information"
            left={props => <List.Icon {...props} icon="account-edit" />}
            onPress={() => Alert.alert('Info', 'Profile settings coming soon')}
          />
        </Card.Content>
      </Card>

      {/* App Settings */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={[styles.sectionTitle, { color: colors.onSurface }]}>
            Application
          </Title>
          
          <List.Item
            title="Notifications"
            description="Manage app notifications"
            left={props => <List.Icon {...props} icon="bell" />}
            onPress={() => Alert.alert('Info', 'Notification settings coming soon')}
          />
          
          <Divider />
          
          <List.Item
            title="Data Backup"
            description="Backup your data"
            left={props => <List.Icon {...props} icon="backup-restore" />}
            onPress={() => Alert.alert('Info', 'Data backup feature coming soon')}
          />
          
          <Divider />
          
          <List.Item
            title="Clear Cache"
            description="Clear app cache and temporary data"
            left={props => <List.Icon {...props} icon="delete-sweep" />}
            onPress={() => Alert.alert('Info', 'Cache cleared successfully')}
          />
        </Card.Content>
      </Card>

      {/* About */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={[styles.sectionTitle, { color: colors.onSurface }]}>
            About
          </Title>
          
          <List.Item
            title="App Version"
            description="1.0.0"
            left={props => <List.Icon {...props} icon="information" />}
          />
          
          <Divider />
          
          <List.Item
            title="Help & Support"
            description="Get help and support"
            left={props => <List.Icon {...props} icon="help-circle" />}
            onPress={() => Alert.alert('Help', 'Contact <NAME_EMAIL>')}
          />
          
          <Divider />
          
          <List.Item
            title="Privacy Policy"
            description="View privacy policy"
            left={props => <List.Icon {...props} icon="shield-account" />}
            onPress={() => Alert.alert('Info', 'Privacy policy coming soon')}
          />
        </Card.Content>
      </Card>

      {/* Logout */}
      <Card style={[styles.card, styles.logoutCard]}>
        <Card.Content>
          <Button
            mode="contained"
            onPress={handleLogout}
            icon="logout"
            buttonColor={colors.error}
            textColor={colors.onError}
            style={styles.logoutButton}
          >
            Logout
          </Button>
        </Card.Content>
      </Card>

      <View style={styles.bottomPadding} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    margin: 16,
    marginBottom: 8,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userDetails: {
    marginLeft: 16,
    flex: 1,
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  userEmail: {
    fontSize: 14,
    marginTop: 2,
  },
  userRole: {
    fontSize: 12,
    marginTop: 4,
    fontWeight: '600',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  logoutCard: {
    marginTop: 8,
  },
  logoutButton: {
    paddingVertical: 4,
  },
  bottomPadding: {
    height: 20,
  },
});

export default SettingsScreen;
