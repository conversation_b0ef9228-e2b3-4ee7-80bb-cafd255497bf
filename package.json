{"name": "bakery-tracker-pro", "version": "1.0.0", "description": "Complete bakery management system with POS, recipes, and analytics", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject"}, "dependencies": {"expo": "~49.0.15", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.6", "react-native-paper": "^5.10.6", "react-native-vector-icons": "^10.0.0", "@react-navigation/native": "^6.1.7", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/stack": "^6.3.17", "react-native-screens": "~3.22.0", "react-native-safe-area-context": "4.6.3", "expo-sqlite": "~11.3.3", "expo-secure-store": "~12.3.1", "expo-font": "~11.4.0", "@expo/vector-icons": "^13.0.0", "react-native-chart-kit": "^6.12.0", "react-native-svg": "13.9.0", "expo-linear-gradient": "~12.3.0", "react-native-modal": "^13.0.1", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "expo-haptics": "~12.4.0", "expo-constants": "~14.4.2", "expo-device": "~5.4.0", "expo-notifications": "~0.20.1", "react-native-uuid": "^2.0.1", "date-fns": "^2.30.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "typescript": "^5.1.3"}, "keywords": ["bakery", "pos", "management", "react-native", "expo", "recipes", "inventory", "analytics"], "author": "<PERSON><PERSON> Tracker Pro", "license": "MIT", "private": true}