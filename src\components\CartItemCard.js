import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, Text, IconButton } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useCart } from '../context/CartContext';

const CartItemCard = ({ cartItem }) => {
  const { colors } = useTheme();
  const { updateCartItemQuantity, removeFromCart } = useCart();

  const handleQuantityChange = (newQuantity) => {
    if (newQuantity <= 0) {
      removeFromCart(cartItem.id);
    } else {
      updateCartItemQuantity(cartItem.id, newQuantity);
    }
  };

  const formatPrice = (price) => {
    return `₹${price.toFixed(2)}`;
  };

  const getTotalPrice = () => {
    return cartItem.price * cartItem.quantity;
  };

  return (
    <Card style={styles.card}>
      <Card.Content style={styles.content}>
        {/* Product Info */}
        <View style={styles.productInfo}>
          <View style={styles.nameContainer}>
            <Text style={[styles.productName, { color: colors.onSurface }]} numberOfLines={2}>
              {cartItem.product_name || cartItem.productName}
            </Text>
            {(cartItem.is_popular || cartItem.isPopular) && (
              <MaterialCommunityIcons
                name="star"
                size={14}
                color={colors.primary}
                style={styles.starIcon}
              />
            )}
          </View>
          
          <Text style={[styles.unitPrice, { color: colors.onSurfaceVariant }]}>
            {formatPrice(cartItem.price)} each
          </Text>
        </View>

        {/* Quantity Controls */}
        <View style={styles.quantityContainer}>
          <View style={styles.quantityControls}>
            <IconButton
              icon="minus"
              size={20}
              onPress={() => handleQuantityChange(cartItem.quantity - 1)}
              style={styles.quantityButton}
              iconColor={colors.onSurfaceVariant}
            />
            
            <Text style={[styles.quantity, { color: colors.onSurface }]}>
              {cartItem.quantity}
            </Text>
            
            <IconButton
              icon="plus"
              size={20}
              onPress={() => handleQuantityChange(cartItem.quantity + 1)}
              style={styles.quantityButton}
              iconColor={colors.primary}
            />
          </View>

          {/* Total Price */}
          <Text style={[styles.totalPrice, { color: colors.primary }]}>
            {formatPrice(getTotalPrice())}
          </Text>
        </View>

        {/* Remove Button */}
        <IconButton
          icon="delete"
          size={20}
          onPress={() => removeFromCart(cartItem.id)}
          style={styles.removeButton}
          iconColor={colors.error}
        />
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginVertical: 4,
    elevation: 1,
  },
  content: {
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  productInfo: {
    flex: 1,
    marginRight: 12,
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  productName: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
    marginRight: 4,
  },
  starIcon: {
    marginTop: 1,
  },
  unitPrice: {
    fontSize: 12,
  },
  quantityContainer: {
    alignItems: 'center',
    marginRight: 8,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  quantityButton: {
    margin: 0,
    width: 32,
    height: 32,
  },
  quantity: {
    fontSize: 16,
    fontWeight: 'bold',
    marginHorizontal: 12,
    minWidth: 24,
    textAlign: 'center',
  },
  totalPrice: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  removeButton: {
    margin: 0,
    width: 32,
    height: 32,
  },
});

export default CartItemCard;
