import React, { createContext, useContext, useState, useEffect } from 'react';
import * as SecureStore from 'expo-secure-store';
import { UserRole } from '../types';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Demo credentials (as per overview.txt)
  const DEMO_USERS = {
    '<EMAIL>': {
      id: 1,
      email: '<EMAIL>',
      password: 'admin123',
      name: 'Admin User',
      role: UserRole.ADMIN,
      phone: '+91-**********'
    },
    '<EMAIL>': {
      id: 2,
      email: '<EMAIL>',
      password: 'staff123',
      name: 'Staff User',
      role: UserRole.STAFF,
      phone: '+91-**********'
    }
  };

  useEffect(() => {
    checkAuthState();
  }, []);

  const checkAuthState = async () => {
    try {
      const userToken = await SecureStore.getItemAsync('userToken');
      const userData = await SecureStore.getItemAsync('userData');
      
      if (userToken && userData) {
        setUser(JSON.parse(userData));
      }
    } catch (error) {
      console.error('Error checking auth state:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email, password) => {
    try {
      const demoUser = DEMO_USERS[email.toLowerCase()];
      
      if (!demoUser || demoUser.password !== password) {
        throw new Error('Invalid email or password');
      }

      // Create user session
      const userSession = {
        id: demoUser.id,
        email: demoUser.email,
        name: demoUser.name,
        role: demoUser.role,
        phone: demoUser.phone,
        loginTime: new Date().toISOString()
      };

      // Store in secure storage
      await SecureStore.setItemAsync('userToken', `token_${demoUser.id}_${Date.now()}`);
      await SecureStore.setItemAsync('userData', JSON.stringify(userSession));

      setUser(userSession);
      return { success: true, user: userSession };
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: error.message };
    }
  };

  const logout = async () => {
    try {
      await SecureStore.deleteItemAsync('userToken');
      await SecureStore.deleteItemAsync('userData');
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const switchRole = async (newRole) => {
    if (!user) return;

    try {
      const updatedUser = { ...user, role: newRole };
      await SecureStore.setItemAsync('userData', JSON.stringify(updatedUser));
      setUser(updatedUser);
    } catch (error) {
      console.error('Role switch error:', error);
    }
  };

  const isAdmin = () => user?.role === UserRole.ADMIN;
  const isStaff = () => user?.role === UserRole.STAFF;

  const value = {
    user,
    isLoading,
    login,
    logout,
    switchRole,
    isAdmin,
    isStaff,
    isAuthenticated: !!user
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
