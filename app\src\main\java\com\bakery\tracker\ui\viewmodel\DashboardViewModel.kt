package com.bakery.tracker.ui.viewmodel

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bakery.tracker.data.models.Ingredient
import com.bakery.tracker.data.repository.IngredientRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the Dashboard screen
 */
@HiltViewModel
class DashboardViewModel @Inject constructor(
    private val ingredientRepository: IngredientRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(DashboardUiState())
    val uiState: StateFlow<DashboardUiState> = _uiState.asStateFlow()
    
    init {
        loadDashboardData()
    }
    
    /**
     * Load dashboard data
     */
    private fun loadDashboardData() {
        viewModelScope.launch {
            // Load low stock ingredients
            ingredientRepository.getLowStockIngredients().collect { ingredients ->
                _uiState.value = _uiState.value.copy(
                    lowStockIngredients = ingredients
                )
            }
        }
        
        // Load mock data for demo
        loadMockData()
    }
    
    /**
     * Refresh dashboard data
     */
    fun refreshData() {
        loadDashboardData()
    }
    
    /**
     * Navigate to stock screen for specific ingredient
     */
    fun navigateToStock(ingredientId: Long) {
        // This would typically use navigation
        // For now, it's a placeholder
    }
    
    /**
     * Load mock data for demonstration
     */
    private fun loadMockData() {
        val mockQuickStats = listOf(
            QuickStat(
                title = "Today's Production",
                value = "156",
                subtitle = "units",
                icon = Icons.Default.Factory
            ),
            QuickStat(
                title = "Revenue",
                value = "$1,234",
                subtitle = "today",
                icon = Icons.Default.AttachMoney
            ),
            QuickStat(
                title = "Orders",
                value = "23",
                subtitle = "pending",
                icon = Icons.Default.ShoppingCart
            ),
            QuickStat(
                title = "Stock Items",
                value = "45",
                subtitle = "low stock",
                icon = Icons.Default.Warning
            )
        )
        
        val mockTodayProduction = listOf(
            ProductionData("Bread", 45),
            ProductionData("Cakes", 23),
            ProductionData("Pastries", 67),
            ProductionData("Cookies", 21)
        )
        
        val mockWeeklyRevenue = listOf(
            RevenueData("Mon", 850.0),
            RevenueData("Tue", 920.0),
            RevenueData("Wed", 1100.0),
            RevenueData("Thu", 980.0),
            RevenueData("Fri", 1250.0),
            RevenueData("Sat", 1400.0),
            RevenueData("Sun", 1150.0)
        )
        
        val mockRecentActivities = listOf(
            RecentActivity(
                title = "Chocolate Cake produced",
                time = "2 minutes ago",
                icon = Icons.Default.Factory
            ),
            RecentActivity(
                title = "Order #123 completed",
                time = "15 minutes ago",
                icon = Icons.Default.CheckCircle
            ),
            RecentActivity(
                title = "Flour stock updated",
                time = "1 hour ago",
                icon = Icons.Default.Inventory
            ),
            RecentActivity(
                title = "New recipe added",
                time = "2 hours ago",
                icon = Icons.Default.MenuBook
            )
        )
        
        _uiState.value = _uiState.value.copy(
            quickStats = mockQuickStats,
            todayProduction = mockTodayProduction,
            weeklyRevenue = mockWeeklyRevenue,
            recentActivities = mockRecentActivities
        )
    }
}

/**
 * UI state for the Dashboard screen
 */
data class DashboardUiState(
    val isLoading: Boolean = false,
    val quickStats: List<QuickStat> = emptyList(),
    val lowStockIngredients: List<Ingredient> = emptyList(),
    val todayProduction: List<ProductionData> = emptyList(),
    val weeklyRevenue: List<RevenueData> = emptyList(),
    val recentActivities: List<RecentActivity> = emptyList(),
    val error: String? = null
)

/**
 * Data class for quick stats
 */
data class QuickStat(
    val title: String,
    val value: String,
    val subtitle: String,
    val icon: ImageVector
)

/**
 * Data class for production data
 */
data class ProductionData(
    val product: String,
    val quantity: Int
)

/**
 * Data class for revenue data
 */
data class RevenueData(
    val day: String,
    val amount: Double
)

/**
 * Data class for recent activities
 */
data class RecentActivity(
    val title: String,
    val time: String,
    val icon: ImageVector
)
