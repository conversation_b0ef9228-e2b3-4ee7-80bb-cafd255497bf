import React, { createContext, useContext, useState, useEffect } from 'react';
import { db } from '../database/DatabaseManager';
import { createCartItem, calculateCartSummary, generateBillNumber } from '../types';
import uuid from 'react-native-uuid';

const CartContext = createContext();

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

export const CartProvider = ({ children }) => {
  const [cartItems, setCartItems] = useState([]);
  const [cartSummary, setCartSummary] = useState({
    items: [],
    subtotal: 0,
    itemCount: 0,
    uniqueProductCount: 0
  });
  const [sessionId] = useState(() => uuid.v4());

  useEffect(() => {
    loadCartItems();
  }, []);

  useEffect(() => {
    // Recalculate summary when cart items change (as per overview.txt)
    const summary = calculateCartSummary(cartItems);
    setCartSummary(summary);
  }, [cartItems]);

  const loadCartItems = () => {
    db.transaction(tx => {
      tx.executeSql(
        'SELECT * FROM cart WHERE session_id = ? ORDER BY created_at DESC',
        [sessionId],
        (_, { rows }) => {
          setCartItems(rows._array);
        },
        (_, error) => {
          console.error('Error loading cart items:', error);
        }
      );
    });
  };

  const addToCart = (product) => {
    return new Promise((resolve, reject) => {
      // Check if product already exists in cart
      const existingItem = cartItems.find(item => item.product_id === product.id);

      if (existingItem) {
        // Update quantity
        updateCartItemQuantity(existingItem.id, existingItem.quantity + 1)
          .then(resolve)
          .catch(reject);
      } else {
        // Add new item
        const cartItem = createCartItem({
          productId: product.id,
          productName: product.name,
          price: product.sellingPrice || product.selling_price,
          quantity: 1,
          category: product.category,
          isPopular: product.isPopular || product.is_popular,
          sessionId
        });

        db.transaction(tx => {
          tx.executeSql(
            `INSERT INTO cart (product_id, product_name, price, quantity, category, is_popular, session_id) 
             VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [cartItem.productId, cartItem.productName, cartItem.price, cartItem.quantity, 
             cartItem.category, cartItem.isPopular ? 1 : 0, cartItem.sessionId],
            (_, result) => {
              loadCartItems();
              resolve(result);
            },
            (_, error) => {
              console.error('Error adding to cart:', error);
              reject(error);
            }
          );
        });
      }
    });
  };

  const updateCartItemQuantity = (cartItemId, newQuantity) => {
    return new Promise((resolve, reject) => {
      if (newQuantity <= 0) {
        removeFromCart(cartItemId).then(resolve).catch(reject);
        return;
      }

      db.transaction(tx => {
        tx.executeSql(
          'UPDATE cart SET quantity = ? WHERE id = ?',
          [newQuantity, cartItemId],
          (_, result) => {
            loadCartItems();
            resolve(result);
          },
          (_, error) => {
            console.error('Error updating cart item quantity:', error);
            reject(error);
          }
        );
      });
    });
  };

  const removeFromCart = (cartItemId) => {
    return new Promise((resolve, reject) => {
      db.transaction(tx => {
        tx.executeSql(
          'DELETE FROM cart WHERE id = ?',
          [cartItemId],
          (_, result) => {
            loadCartItems();
            resolve(result);
          },
          (_, error) => {
            console.error('Error removing from cart:', error);
            reject(error);
          }
        );
      });
    });
  };

  const clearCart = () => {
    return new Promise((resolve, reject) => {
      db.transaction(tx => {
        tx.executeSql(
          'DELETE FROM cart WHERE session_id = ?',
          [sessionId],
          (_, result) => {
            setCartItems([]);
            resolve(result);
          },
          (_, error) => {
            console.error('Error clearing cart:', error);
            reject(error);
          }
        );
      });
    });
  };

  const createOrderFromCart = (customerName = '', customerPhone = '', paymentMethod = 'CASH', discount = 0, tax = 0) => {
    return new Promise((resolve, reject) => {
      if (cartItems.length === 0) {
        reject(new Error('Cart is empty'));
        return;
      }

      const billNumber = generateBillNumber();
      const subtotal = cartSummary.subtotal;
      const total = subtotal - discount + tax;

      db.transaction(tx => {
        // Create order
        tx.executeSql(
          `INSERT INTO orders (bill_number, customer_name, customer_phone, subtotal, discount, tax, total, payment_method) 
           VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [billNumber, customerName, customerPhone, subtotal, discount, tax, total, paymentMethod],
          (_, orderResult) => {
            const orderId = orderResult.insertId;

            // Create order items
            cartItems.forEach(cartItem => {
              tx.executeSql(
                `INSERT INTO order_items (order_id, product_id, product_name, price, quantity, total) 
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [orderId, cartItem.product_id, cartItem.product_name, cartItem.price, 
                 cartItem.quantity, cartItem.price * cartItem.quantity]
              );
            });

            // Clear cart after successful order
            tx.executeSql(
              'DELETE FROM cart WHERE session_id = ?',
              [sessionId],
              () => {
                setCartItems([]);
                resolve({ orderId, billNumber, total });
              }
            );
          },
          (_, error) => {
            console.error('Error creating order:', error);
            reject(error);
          }
        );
      });
    });
  };

  const value = {
    cartItems,
    cartSummary,
    sessionId,
    addToCart,
    updateCartItemQuantity,
    removeFromCart,
    clearCart,
    createOrderFromCart,
    loadCartItems
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};
