import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  Alert,
} from 'react-native';
import {
  Text,
  FAB,
  Searchbar,
  Chip,
  Portal,
  Modal,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useDatabase } from '../context/DatabaseContext';
import { ProductCategory, ProductCategoryDisplayNames } from '../types';
import RecipeCard from '../components/RecipeCard';
import AddRecipeModal from '../components/AddRecipeModal';

const RecipeScreen = () => {
  const [recipes, setRecipes] = useState([]);
  const [filteredRecipes, setFilteredRecipes] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);

  const { colors } = useTheme();
  const { getRecipes, addRecipe, deleteRecipe } = useDatabase();

  useEffect(() => {
    loadRecipes();
  }, []);

  useEffect(() => {
    filterRecipes();
  }, [recipes, selectedCategory, searchQuery]);

  const loadRecipes = async () => {
    try {
      const recipesData = await getRecipes();
      setRecipes(recipesData);
    } catch (error) {
      console.error('Error loading recipes:', error);
      Alert.alert('Error', 'Failed to load recipes');
    } finally {
      setIsLoading(false);
    }
  };

  const filterRecipes = () => {
    let filtered = recipes;

    if (selectedCategory) {
      filtered = filtered.filter(recipe => recipe.category === selectedCategory);
    }

    if (searchQuery) {
      filtered = filtered.filter(recipe =>
        recipe.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        recipe.instructions.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredRecipes(filtered);
  };

  const handleCategorySelect = (category) => {
    setSelectedCategory(selectedCategory === category ? null : category);
  };

  const handleAddRecipe = async (recipeData) => {
    try {
      await addRecipe(recipeData);
      setShowAddModal(false);
      loadRecipes();
      Alert.alert('Success', 'Recipe added successfully');
    } catch (error) {
      console.error('Error adding recipe:', error);
      Alert.alert('Error', 'Failed to add recipe');
    }
  };

  const handleDeleteRecipe = async (recipeId) => {
    Alert.alert(
      'Delete Recipe',
      'Are you sure you want to delete this recipe?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteRecipe(recipeId);
              loadRecipes();
              Alert.alert('Success', 'Recipe deleted successfully');
            } catch (error) {
              console.error('Error deleting recipe:', error);
              Alert.alert('Error', 'Failed to delete recipe');
            }
          },
        },
      ]
    );
  };

  const renderCategoryChips = () => (
    <View style={styles.categoryContainer}>
      <Chip
        mode={selectedCategory === null ? 'flat' : 'outlined'}
        selected={selectedCategory === null}
        onPress={() => handleCategorySelect(null)}
        style={styles.categoryChip}
      >
        All
      </Chip>
      {Object.values(ProductCategory).map(category => (
        <Chip
          key={category}
          mode={selectedCategory === category ? 'flat' : 'outlined'}
          selected={selectedCategory === category}
          onPress={() => handleCategorySelect(category)}
          style={styles.categoryChip}
        >
          {ProductCategoryDisplayNames[category]}
        </Chip>
      ))}
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <MaterialCommunityIcons
        name="book-open-variant"
        size={80}
        color={colors.onSurfaceVariant}
      />
      <Text style={[styles.emptyText, { color: colors.onSurfaceVariant }]}>
        No recipes found
      </Text>
      <Text style={[styles.emptySubtext, { color: colors.onSurfaceVariant }]}>
        {searchQuery || selectedCategory
          ? 'Try adjusting your filters'
          : 'Create your first recipe to get started'}
      </Text>
    </View>
  );

  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <MaterialCommunityIcons name="loading" size={50} color={colors.primary} />
        <Text style={{ color: colors.onBackground, marginTop: 10 }}>
          Loading recipes...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <Text style={[styles.headerTitle, { color: colors.onSurface }]}>
          Recipe Management
        </Text>
        <Searchbar
          placeholder="Search recipes..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
          inputStyle={{ fontSize: 14 }}
        />
      </View>

      {/* Category Filters */}
      {renderCategoryChips()}

      {/* Recipes List */}
      {filteredRecipes.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={filteredRecipes}
          renderItem={({ item }) => (
            <RecipeCard
              recipe={item}
              onDelete={() => handleDeleteRecipe(item.id)}
            />
          )}
          keyExtractor={item => item.id.toString()}
          style={styles.recipesList}
          contentContainerStyle={styles.recipesContent}
          showsVerticalScrollIndicator={false}
        />
      )}

      {/* Add Recipe FAB */}
      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: colors.primary }]}
        onPress={() => setShowAddModal(true)}
        label="Add Recipe"
      />

      {/* Add Recipe Modal */}
      <Portal>
        <Modal
          visible={showAddModal}
          onDismiss={() => setShowAddModal(false)}
          contentContainerStyle={[styles.modal, { backgroundColor: colors.surface }]}
        >
          <AddRecipeModal
            onClose={() => setShowAddModal(false)}
            onSave={handleAddRecipe}
          />
        </Modal>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 16,
    elevation: 2,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  searchbar: {
    elevation: 0,
    borderRadius: 8,
  },
  categoryContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    flexWrap: 'wrap',
  },
  categoryChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  recipesList: {
    flex: 1,
  },
  recipesContent: {
    padding: 16,
    paddingBottom: 100,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  modal: {
    margin: 20,
    borderRadius: 12,
    maxHeight: '90%',
  },
});

export default RecipeScreen;
