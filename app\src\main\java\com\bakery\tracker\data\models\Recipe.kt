package com.bakery.tracker.data.models

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Represents a recipe for a bakery product.
 * 
 * @param id Unique identifier for the recipe
 * @param productId ID of the product this recipe creates
 * @param name Name of the recipe
 * @param description Recipe description or instructions
 * @param batchSize Number of units this recipe produces
 * @param preparationTimeMinutes Time required to prepare in minutes
 * @param bakingTimeMinutes Time required to bake in minutes
 * @param isActive Whether this recipe is currently in use
 * @param createdAt Timestamp when recipe was created
 * @param updatedAt Timestamp when recipe was last updated
 */
@Entity(tableName = "recipes")
data class Recipe(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val productId: Long,
    val name: String,
    val description: String = "",
    val batchSize: Int,
    val preparationTimeMinutes: Int = 0,
    val bakingTimeMinutes: Int = 0,
    val isActive: Boolean = true,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    /**
     * Calculates total time required for this recipe
     */
    fun getTotalTimeMinutes(): Int = preparationTimeMinutes + bakingTimeMinutes
    
    /**
     * Formats total time as a readable string
     */
    fun getFormattedTotalTime(): String {
        val totalMinutes = getTotalTimeMinutes()
        val hours = totalMinutes / 60
        val minutes = totalMinutes % 60
        
        return when {
            hours > 0 && minutes > 0 -> "${hours}h ${minutes}m"
            hours > 0 -> "${hours}h"
            minutes > 0 -> "${minutes}m"
            else -> "0m"
        }
    }
}
