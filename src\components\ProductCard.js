import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, Text, Button, Chip } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { ProductCategoryDisplayNames } from '../types';

const ProductCard = ({ product, onAddToCart, style }) => {
  const { colors } = useTheme();

  const formatPrice = (price) => {
    return `₹${price.toFixed(2)}`;
  };

  return (
    <Card style={[styles.card, style]} onPress={onAddToCart}>
      <Card.Content style={styles.content}>
        {/* Product Header */}
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Text style={[styles.title, { color: colors.onSurface }]} numberOfLines={2}>
              {product.name}
            </Text>
            {(product.is_popular || product.isPopular) && (
              <MaterialCommunityIcons
                name="star"
                size={16}
                color={colors.primary}
                style={styles.starIcon}
              />
            )}
          </View>
        </View>

        {/* Description */}
        {product.description && (
          <Text
            style={[styles.description, { color: colors.onSurfaceVariant }]}
            numberOfLines={2}
          >
            {product.description}
          </Text>
        )}

        {/* Category */}
        <Chip
          mode="outlined"
          compact
          style={styles.categoryChip}
          textStyle={styles.categoryText}
        >
          {ProductCategoryDisplayNames[product.category]}
        </Chip>

        {/* Price and Servings */}
        <View style={styles.priceContainer}>
          <View>
            <Text style={[styles.price, { color: colors.primary }]}>
              {formatPrice(product.selling_price || product.sellingPrice)}
            </Text>
            {product.servings > 1 && (
              <Text style={[styles.servings, { color: colors.onSurfaceVariant }]}>
                {product.servings} servings
              </Text>
            )}
          </View>

          <Button
            mode="contained"
            onPress={onAddToCart}
            style={styles.addButton}
            contentStyle={styles.addButtonContent}
            compact
          >
            <MaterialCommunityIcons name="plus" size={16} color={colors.onPrimary} />
          </Button>
        </View>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    elevation: 2,
    marginVertical: 4,
  },
  content: {
    padding: 12,
  },
  header: {
    marginBottom: 8,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 8,
  },
  starIcon: {
    marginTop: 2,
  },
  description: {
    fontSize: 12,
    marginBottom: 8,
    lineHeight: 16,
  },
  categoryChip: {
    alignSelf: 'flex-start',
    marginBottom: 12,
    height: 24,
  },
  categoryText: {
    fontSize: 10,
  },
  priceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  price: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  servings: {
    fontSize: 10,
    marginTop: 2,
  },
  addButton: {
    minWidth: 36,
    borderRadius: 18,
  },
  addButtonContent: {
    height: 36,
    width: 36,
  },
});

export default ProductCard;
