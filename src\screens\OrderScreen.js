import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card, Button, Chip } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';

const OrderScreen = () => {
  const { colors } = useTheme();
  const { user } = useAuth();

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <MaterialCommunityIcons
          name="clipboard-list"
          size={80}
          color={colors.primary}
        />
        <Text style={[styles.title, { color: colors.onBackground }]}>
          Order Management
        </Text>
        <Text style={[styles.subtitle, { color: colors.onSurfaceVariant }]}>
          View orders, manage billing, and track customer information
        </Text>
        
        <Chip mode="outlined" icon="shield-account" style={styles.adminChip}>
          Admin Only Feature
        </Chip>
        
        <Card style={styles.featureCard}>
          <Card.Content>
            <Text style={[styles.featureTitle, { color: colors.onSurface }]}>
              Available Features:
            </Text>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • View all orders and bills
            </Text>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • Customer management
            </Text>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • Payment tracking
            </Text>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • Sales reports
            </Text>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • Bill generation (BT-YYYYMMDD-XXXX)
            </Text>
          </Card.Content>
        </Card>

        <Button mode="contained" onPress={() => {}}>
          View Today's Orders
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  content: {
    alignItems: 'center',
    maxWidth: 400,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  adminChip: {
    marginBottom: 20,
  },
  featureCard: {
    width: '100%',
    marginBottom: 30,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  featureItem: {
    fontSize: 14,
    marginBottom: 5,
  },
});

export default OrderScreen;
