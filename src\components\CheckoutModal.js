import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import {
  Text,
  Button,
  TextInput,
  RadioButton,
  Card,
  Title,
  Divider,
  IconButton,
} from 'react-native-paper';
import { useTheme } from '../context/ThemeContext';
import { useCart } from '../context/CartContext';
import { PaymentMethod, PaymentMethodDisplayNames } from '../types';

const CheckoutModal = ({ onClose, onSuccess }) => {
  const [customerName, setCustomerName] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [paymentMethod, setPaymentMethod] = useState(PaymentMethod.CASH);
  const [isProcessing, setIsProcessing] = useState(false);

  const { colors } = useTheme();
  const { cartSummary, createOrderFromCart } = useCart();

  const formatPrice = (price) => {
    return `₹${price.toFixed(2)}`;
  };

  const handleCheckout = async () => {
    if (!customerName.trim()) {
      Alert.alert('Error', 'Please enter customer name');
      return;
    }

    if (!customerPhone.trim()) {
      Alert.alert('Error', 'Please enter customer phone number');
      return;
    }

    setIsProcessing(true);
    try {
      const result = await createOrderFromCart(
        customerName.trim(),
        customerPhone.trim(),
        paymentMethod
      );

      Alert.alert(
        'Order Successful!',
        `Order ${result.billNumber} created successfully.\nTotal: ${formatPrice(result.total)}`,
        [{ text: 'OK', onPress: onSuccess }]
      );
    } catch (error) {
      console.error('Checkout error:', error);
      Alert.alert('Error', 'Failed to process order. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Title style={[styles.title, { color: colors.onSurface }]}>
          Checkout
        </Title>
        <IconButton
          icon="close"
          size={24}
          onPress={onClose}
        />
      </View>

      <Divider />

      {/* Order Summary */}
      <Card style={[styles.summaryCard, { backgroundColor: colors.surfaceVariant }]}>
        <Card.Content>
          <Text style={[styles.sectionTitle, { color: colors.onSurface }]}>
            Order Summary
          </Text>
          
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: colors.onSurfaceVariant }]}>
              Items:
            </Text>
            <Text style={[styles.summaryValue, { color: colors.onSurfaceVariant }]}>
              {cartSummary.itemCount}
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: colors.onSurfaceVariant }]}>
              Products:
            </Text>
            <Text style={[styles.summaryValue, { color: colors.onSurfaceVariant }]}>
              {cartSummary.uniqueProductCount}
            </Text>
          </View>

          <Divider style={styles.summaryDivider} />

          <View style={styles.summaryRow}>
            <Text style={[styles.totalLabel, { color: colors.onSurface }]}>
              Total Amount:
            </Text>
            <Text style={[styles.totalValue, { color: colors.primary }]}>
              {formatPrice(cartSummary.subtotal)}
            </Text>
          </View>
        </Card.Content>
      </Card>

      {/* Customer Information */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.onSurface }]}>
          Customer Information
        </Text>

        <TextInput
          label="Customer Name *"
          value={customerName}
          onChangeText={setCustomerName}
          mode="outlined"
          style={styles.input}
          disabled={isProcessing}
        />

        <TextInput
          label="Phone Number *"
          value={customerPhone}
          onChangeText={setCustomerPhone}
          mode="outlined"
          keyboardType="phone-pad"
          style={styles.input}
          disabled={isProcessing}
        />
      </View>

      {/* Payment Method */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.onSurface }]}>
          Payment Method
        </Text>

        {Object.values(PaymentMethod).map(method => (
          <View key={method} style={styles.radioRow}>
            <RadioButton
              value={method}
              status={paymentMethod === method ? 'checked' : 'unchecked'}
              onPress={() => setPaymentMethod(method)}
              disabled={isProcessing}
            />
            <Text
              style={[styles.radioLabel, { color: colors.onSurface }]}
              onPress={() => setPaymentMethod(method)}
            >
              {PaymentMethodDisplayNames[method]}
            </Text>
          </View>
        ))}
      </View>

      {/* Actions */}
      <View style={styles.actions}>
        <Button
          mode="outlined"
          onPress={onClose}
          style={styles.actionButton}
          disabled={isProcessing}
        >
          Cancel
        </Button>
        <Button
          mode="contained"
          onPress={handleCheckout}
          style={styles.actionButton}
          loading={isProcessing}
          disabled={isProcessing}
        >
          {isProcessing ? 'Processing...' : 'Complete Order'}
        </Button>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    maxHeight: '90%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  summaryCard: {
    margin: 16,
  },
  section: {
    padding: 16,
    paddingTop: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  summaryDivider: {
    marginVertical: 8,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  totalValue: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  input: {
    marginBottom: 12,
  },
  radioRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  radioLabel: {
    fontSize: 16,
    marginLeft: 8,
  },
  actions: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});

export default CheckoutModal;
