package com.bakery.tracker.data.local

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.bakery.tracker.data.local.dao.*
import com.bakery.tracker.data.models.*

/**
 * Room database for the Bakery Tracker application
 */
@Database(
    entities = [
        Ingredient::class,
        Product::class,
        Recipe::class,
        RecipeIngredient::class,
        ProductionLog::class,
        Bill::class,
        BillItem::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class BakeryDatabase : RoomDatabase() {
    
    abstract fun ingredientDao(): IngredientDao
    abstract fun productDao(): ProductDao
    abstract fun recipeDao(): RecipeDao
    abstract fun productionDao(): ProductionDao
    abstract fun billDao(): BillDao
    
    companion object {
        @Volatile
        private var INSTANCE: BakeryDatabase? = null
        
        fun getDatabase(context: Context): BakeryDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    BakeryDatabase::class.java,
                    "bakery_database"
                )
                    .fallbackToDestructiveMigration()
                    .build()
                INSTANCE = instance
                instance
            }
        }
    }
}

/**
 * Type converters for Room database
 */
class Converters {
    
    @androidx.room.TypeConverter
    fun fromPaymentStatus(status: PaymentStatus): String {
        return status.name
    }
    
    @androidx.room.TypeConverter
    fun toPaymentStatus(status: String): PaymentStatus {
        return PaymentStatus.valueOf(status)
    }
}
