package com.bakery.tracker.data.repository

import com.bakery.tracker.data.local.dao.ProductDao
import com.bakery.tracker.data.models.Product
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing product data operations
 */
@Singleton
class ProductRepository @Inject constructor(
    private val productDao: ProductDao
) {
    
    /**
     * Get all products as a Flow
     */
    fun getAllProducts(): Flow<List<Product>> = productDao.getAllProducts()
    
    /**
     * Get all active products
     */
    fun getActiveProducts(): Flow<List<Product>> = productDao.getActiveProducts()
    
    /**
     * Get products by category
     */
    fun getProductsByCategory(category: String): Flow<List<Product>> = 
        productDao.getProductsByCategory(category)
    
    /**
     * Get all categories
     */
    fun getAllCategories(): Flow<List<String>> = productDao.getAllCategories()
    
    /**
     * Get product by ID
     */
    suspend fun getProductById(id: Long): Product? = productDao.getProductById(id)
    
    /**
     * Get product by name
     */
    suspend fun getProductByName(name: String): Product? = productDao.getProductByName(name)
    
    /**
     * Search products by name
     */
    fun searchProducts(searchQuery: String): Flow<List<Product>> = 
        productDao.searchProducts(searchQuery)
    
    /**
     * Insert a new product
     */
    suspend fun insertProduct(product: Product): Long = productDao.insertProduct(product)
    
    /**
     * Insert multiple products
     */
    suspend fun insertProducts(products: List<Product>): List<Long> = 
        productDao.insertProducts(products)
    
    /**
     * Update an existing product
     */
    suspend fun updateProduct(product: Product) = productDao.updateProduct(product)
    
    /**
     * Update product cost price
     */
    suspend fun updateProductCostPrice(id: Long, newCostPrice: Double) = 
        productDao.updateProductCostPrice(id, newCostPrice)
    
    /**
     * Update product selling price
     */
    suspend fun updateProductSellingPrice(id: Long, newSellingPrice: Double) = 
        productDao.updateProductSellingPrice(id, newSellingPrice)
    
    /**
     * Set manual cost override
     */
    suspend fun setManualCostOverride(id: Long, manualCost: Double?) = 
        productDao.setManualCostOverride(id, manualCost)
    
    /**
     * Toggle product active status
     */
    suspend fun toggleProductActiveStatus(id: Long, isActive: Boolean) = 
        productDao.toggleProductActiveStatus(id, isActive)
    
    /**
     * Delete a product
     */
    suspend fun deleteProduct(product: Product) = productDao.deleteProduct(product)
    
    /**
     * Delete product by ID
     */
    suspend fun deleteProductById(id: Long) = productDao.deleteProductById(id)
    
    /**
     * Get total count of active products
     */
    suspend fun getActiveProductCount(): Int = productDao.getActiveProductCount()
    
    /**
     * Get products with highest profit margin
     */
    suspend fun getTopProfitableProducts(limit: Int = 10): List<Product> = 
        productDao.getTopProfitableProducts(limit)
    
    /**
     * Calculate and update product cost based on recipe ingredients
     */
    suspend fun calculateAndUpdateProductCost(productId: Long, ingredientCosts: Map<Long, Double>) {
        // This would involve getting the recipe for the product and calculating total cost
        // Implementation would depend on recipe repository
        // For now, this is a placeholder
    }
    
    /**
     * Get products that need price updates
     */
    suspend fun getProductsNeedingPriceUpdate(): List<Product> {
        // Products where cost price might be outdated compared to ingredient prices
        return emptyList() // Placeholder implementation
    }
}
