import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  Title,
  Paragraph,
  Chip,
  ActivityIndicator,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';

const LoginScreen = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  
  const { login } = useAuth();
  const { colors } = useTheme();

  const handleLogin = async () => {
    if (!email.trim() || !password.trim()) {
      Alert.alert('Error', 'Please enter both email and password');
      return;
    }

    setIsLoading(true);
    try {
      const result = await login(email.trim(), password);
      if (!result.success) {
        Alert.alert('Login Failed', result.error);
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const fillDemoCredentials = (userType) => {
    if (userType === 'admin') {
      setEmail('<EMAIL>');
      setPassword('admin123');
    } else {
      setEmail('<EMAIL>');
      setPassword('staff123');
    }
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.header}>
          <MaterialCommunityIcons
            name="bakery"
            size={80}
            color={colors.primary}
            style={styles.logo}
          />
          <Title style={[styles.title, { color: colors.onBackground }]}>
            Bakery Tracker Pro
          </Title>
          <Paragraph style={[styles.subtitle, { color: colors.onSurfaceVariant }]}>
            Complete bakery management system
          </Paragraph>
        </View>

        <Card style={[styles.card, { backgroundColor: colors.surface }]}>
          <Card.Content>
            <Title style={[styles.cardTitle, { color: colors.onSurface }]}>
              Sign In
            </Title>

            <View style={styles.demoSection}>
              <Text style={[styles.demoLabel, { color: colors.onSurfaceVariant }]}>
                Demo Accounts:
              </Text>
              <View style={styles.demoChips}>
                <Chip
                  mode="outlined"
                  onPress={() => fillDemoCredentials('admin')}
                  style={styles.demoChip}
                  textStyle={{ fontSize: 12 }}
                >
                  Admin Demo
                </Chip>
                <Chip
                  mode="outlined"
                  onPress={() => fillDemoCredentials('staff')}
                  style={styles.demoChip}
                  textStyle={{ fontSize: 12 }}
                >
                  Staff Demo
                </Chip>
              </View>
            </View>

            <TextInput
              label="Email"
              value={email}
              onChangeText={setEmail}
              mode="outlined"
              keyboardType="email-address"
              autoCapitalize="none"
              autoComplete="email"
              left={<TextInput.Icon icon="email" />}
              style={styles.input}
              disabled={isLoading}
            />

            <TextInput
              label="Password"
              value={password}
              onChangeText={setPassword}
              mode="outlined"
              secureTextEntry={!showPassword}
              autoComplete="password"
              left={<TextInput.Icon icon="lock" />}
              right={
                <TextInput.Icon
                  icon={showPassword ? 'eye-off' : 'eye'}
                  onPress={() => setShowPassword(!showPassword)}
                />
              }
              style={styles.input}
              disabled={isLoading}
            />

            <Button
              mode="contained"
              onPress={handleLogin}
              style={styles.loginButton}
              disabled={isLoading}
              loading={isLoading}
            >
              {isLoading ? 'Signing In...' : 'Sign In'}
            </Button>

            <View style={styles.credentialsInfo}>
              <Text style={[styles.credentialsTitle, { color: colors.onSurfaceVariant }]}>
                Demo Credentials:
              </Text>
              <Text style={[styles.credentialsText, { color: colors.onSurfaceVariant }]}>
                Admin: <EMAIL> / admin123
              </Text>
              <Text style={[styles.credentialsText, { color: colors.onSurfaceVariant }]}>
                Staff: <EMAIL> / staff123
              </Text>
            </View>
          </Card.Content>
        </Card>

        <View style={styles.features}>
          <Text style={[styles.featuresTitle, { color: colors.onBackground }]}>
            Features Include:
          </Text>
          <View style={styles.featuresList}>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • POS System with Shopping Cart
            </Text>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • Recipe Management with Ingredients
            </Text>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • Dashboard Analytics & Reports
            </Text>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • Inventory & Stock Management
            </Text>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • Order Management & Billing
            </Text>
            <Text style={[styles.featureItem, { color: colors.onSurfaceVariant }]}>
              • Role-based Access Control
            </Text>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  logo: {
    marginBottom: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
  },
  card: {
    marginBottom: 20,
    elevation: 4,
  },
  cardTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  demoSection: {
    marginBottom: 20,
  },
  demoLabel: {
    fontSize: 14,
    marginBottom: 8,
    textAlign: 'center',
  },
  demoChips: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 10,
  },
  demoChip: {
    marginHorizontal: 5,
  },
  input: {
    marginBottom: 15,
  },
  loginButton: {
    marginTop: 10,
    marginBottom: 20,
    paddingVertical: 5,
  },
  credentialsInfo: {
    alignItems: 'center',
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  credentialsTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  credentialsText: {
    fontSize: 12,
    marginBottom: 2,
  },
  features: {
    alignItems: 'center',
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  featuresList: {
    alignItems: 'flex-start',
  },
  featureItem: {
    fontSize: 14,
    marginBottom: 5,
  },
});

export default LoginScreen;
