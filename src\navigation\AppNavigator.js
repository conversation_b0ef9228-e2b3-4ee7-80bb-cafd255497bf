import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';
import { UserRole } from '../types';

// Screens
import LoginScreen from '../screens/LoginScreen';
import LoadingScreen from '../screens/LoadingScreen';
import DashboardScreen from '../screens/DashboardScreen';
import POSScreen from '../screens/POSScreen';
import RecipeScreen from '../screens/RecipeScreen';
import ProductionScreen from '../screens/ProductionScreen';
import StockScreen from '../screens/StockScreen';
import OrderScreen from '../screens/OrderScreen';
import PricingScreen from '../screens/PricingScreen';
import StaffScreen from '../screens/StaffScreen';
import SettingsScreen from '../screens/SettingsScreen';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Bottom tab screens for Staff users
const StaffTabNavigator = () => {
  const { colors } = useTheme();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          switch (route.name) {
            case 'Dashboard':
              iconName = 'view-dashboard';
              break;
            case 'POS':
              iconName = 'store';
              break;
            case 'Recipes':
              iconName = 'book-open-variant';
              break;
            case 'Production':
              iconName = 'factory';
              break;
            case 'Stock':
              iconName = 'package-variant';
              break;
            default:
              iconName = 'circle';
          }

          return <MaterialCommunityIcons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.onSurfaceVariant,
        tabBarStyle: {
          backgroundColor: colors.surface,
          borderTopColor: colors.outline,
        },
        headerStyle: {
          backgroundColor: colors.surface,
        },
        headerTintColor: colors.onSurface,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen 
        name="Dashboard" 
        component={DashboardScreen}
        options={{ title: 'Dashboard' }}
      />
      <Tab.Screen 
        name="POS" 
        component={POSScreen}
        options={{ title: 'POS' }}
      />
      <Tab.Screen 
        name="Recipes" 
        component={RecipeScreen}
        options={{ title: 'Recipes' }}
      />
      <Tab.Screen 
        name="Production" 
        component={ProductionScreen}
        options={{ title: 'Production' }}
      />
      <Tab.Screen 
        name="Stock" 
        component={StockScreen}
        options={{ title: 'Stock' }}
      />
    </Tab.Navigator>
  );
};

// Bottom tab screens for Admin users (includes additional screens)
const AdminTabNavigator = () => {
  const { colors } = useTheme();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          switch (route.name) {
            case 'Dashboard':
              iconName = 'view-dashboard';
              break;
            case 'POS':
              iconName = 'store';
              break;
            case 'Recipes':
              iconName = 'book-open-variant';
              break;
            case 'Orders':
              iconName = 'clipboard-list';
              break;
            case 'Settings':
              iconName = 'cog';
              break;
            default:
              iconName = 'circle';
          }

          return <MaterialCommunityIcons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.onSurfaceVariant,
        tabBarStyle: {
          backgroundColor: colors.surface,
          borderTopColor: colors.outline,
        },
        headerStyle: {
          backgroundColor: colors.surface,
        },
        headerTintColor: colors.onSurface,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen 
        name="Dashboard" 
        component={DashboardScreen}
        options={{ title: 'Dashboard' }}
      />
      <Tab.Screen 
        name="POS" 
        component={POSScreen}
        options={{ title: 'POS' }}
      />
      <Tab.Screen 
        name="Recipes" 
        component={RecipeScreen}
        options={{ title: 'Recipes' }}
      />
      <Tab.Screen 
        name="Orders" 
        component={OrderScreen}
        options={{ title: 'Orders' }}
      />
      <Tab.Screen 
        name="Settings" 
        component={SettingsScreen}
        options={{ title: 'Settings' }}
      />
    </Tab.Navigator>
  );
};

// Admin stack navigator for additional screens
const AdminStackNavigator = () => {
  const { colors } = useTheme();

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.surface,
        },
        headerTintColor: colors.onSurface,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen 
        name="AdminTabs" 
        component={AdminTabNavigator}
        options={{ headerShown: false }}
      />
      <Stack.Screen 
        name="Production" 
        component={ProductionScreen}
        options={{ title: 'Production Management' }}
      />
      <Stack.Screen 
        name="Stock" 
        component={StockScreen}
        options={{ title: 'Stock Management' }}
      />
      <Stack.Screen 
        name="Pricing" 
        component={PricingScreen}
        options={{ title: 'Pricing Management' }}
      />
      <Stack.Screen 
        name="Staff" 
        component={StaffScreen}
        options={{ title: 'Staff Management' }}
      />
    </Stack.Navigator>
  );
};

// Main app navigator
const AppNavigator = () => {
  const { user, isLoading } = useAuth();
  const { colors } = useTheme();

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.surface,
        },
        headerTintColor: colors.onSurface,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      {!user ? (
        // Authentication screens
        <Stack.Screen 
          name="Login" 
          component={LoginScreen}
          options={{ headerShown: false }}
        />
      ) : (
        // Main app screens based on user role
        <>
          {user.role === UserRole.ADMIN ? (
            <Stack.Screen 
              name="AdminApp" 
              component={AdminStackNavigator}
              options={{ headerShown: false }}
            />
          ) : (
            <Stack.Screen 
              name="StaffApp" 
              component={StaffTabNavigator}
              options={{ headerShown: false }}
            />
          )}
        </>
      )}
    </Stack.Navigator>
  );
};

export default AppNavigator;
