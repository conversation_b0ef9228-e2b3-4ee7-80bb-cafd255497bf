<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="bakery_background">#FFFBF0</color>
    <color name="bakery_error">#D32F2F</color>
    <color name="bakery_on_background">#1C1B1F</color>
    <color name="bakery_on_error">#FFFFFF</color>
    <color name="bakery_on_primary">#FFFFFF</color>
    <color name="bakery_on_secondary">#000000</color>
    <color name="bakery_on_surface">#1C1B1F</color>
    <color name="bakery_primary">#8B4513</color>
    <color name="bakery_primary_variant">#5D2F0A</color>
    <color name="bakery_secondary">#FFB74D</color>
    <color name="bakery_secondary_variant">#FF8F00</color>
    <color name="bakery_surface">#FFFFFF</color>
    <color name="black">#000000</color>
    <color name="loss_red">#F44336</color>
    <color name="low_stock_warning">#FF9800</color>
    <color name="profit_green">#4CAF50</color>
    <color name="transparent">#00000000</color>
    <color name="white">#FFFFFF</color>
    <string name="add">Add</string>
    <string name="app_name">Bakery Tracker</string>
    <string name="bill_capture_camera">Capture Bill</string>
    <string name="bill_capture_coming_soon">Bill Capture</string>
    <string name="bill_capture_description">Use CameraX to capture bills, preview images, and map ingredients with undo functionality.</string>
    <string name="bill_capture_subtitle">Capture and manage bills</string>
    <string name="bill_capture_title">Bill Capture</string>
    <string name="cancel">Cancel</string>
    <string name="cd_app_logo">App Logo</string>
    <string name="cd_logout">Logout</string>
    <string name="cd_refresh">Refresh</string>
    <string name="cd_theme">Theme</string>
    <string name="cd_toggle_password">Toggle password visibility</string>
    <string name="cd_user">User</string>
    <string name="cd_warning">Warning</string>
    <string name="coming_soon">Coming Soon</string>
    <string name="dashboard_low_stock_alerts">Low Stock Alerts</string>
    <string name="dashboard_no_activity">No recent activity</string>
    <string name="dashboard_production_chart">Today\'s Production</string>
    <string name="dashboard_recent_activity">Recent Activity</string>
    <string name="dashboard_refresh">Refresh</string>
    <string name="dashboard_revenue_chart">Weekly Revenue</string>
    <string name="dashboard_subtitle">Today\'s Overview</string>
    <string name="dashboard_title">Dashboard</string>
    <string name="delete">Delete</string>
    <string name="edit">Edit</string>
    <string name="error">Error</string>
    <string name="filter">Filter</string>
    <string name="loading">Loading...</string>
    <string name="login_button">Login</string>
    <string name="login_demo_credentials">Demo Credentials</string>
    <string name="login_demo_info">Username: demo\nPassword: demo123\nRole: Staff or Admin</string>
    <string name="login_password">Password</string>
    <string name="login_role_admin">Admin</string>
    <string name="login_role_staff">Staff</string>
    <string name="login_subtitle">Production Management System</string>
    <string name="login_title">Bakery Tracker Pro</string>
    <string name="login_username">Username</string>
    <string name="low_stock_current">Current: %1$s %2$s</string>
    <string name="low_stock_restock">Restock</string>
    <string name="low_stock_threshold">Threshold: %1$s %2$s</string>
    <string name="nav_bills">Bills</string>
    <string name="nav_dashboard">Dashboard</string>
    <string name="nav_orders">Orders</string>
    <string name="nav_pricing">Pricing</string>
    <string name="nav_production">Production</string>
    <string name="nav_recipes">Recipes</string>
    <string name="nav_settings">Settings</string>
    <string name="nav_staff">Staff</string>
    <string name="nav_stock">Stock</string>
    <string name="no_data">No data available</string>
    <string name="orders_coming_soon">Order Management</string>
    <string name="orders_description">Create customer orders, generate PDF invoices with QR codes, and export billing data to CSV.</string>
    <string name="orders_new">New Order</string>
    <string name="orders_subtitle">Manage customer orders</string>
    <string name="orders_title">Orders</string>
    <string name="pricing_coming_soon">Pricing Management</string>
    <string name="pricing_description">Calculate real-time unit costs based on ingredient prices with manual override options.</string>
    <string name="pricing_subtitle">Manage product pricing</string>
    <string name="pricing_title">Pricing</string>
    <string name="production_coming_soon">Production Tracking</string>
    <string name="production_description">Record production quantities, track ingredient usage, and manage batch information with undo functionality.</string>
    <string name="production_record">Record Production</string>
    <string name="production_subtitle">Track daily production</string>
    <string name="production_title">Production</string>
    <string name="recipes_add">Add Recipe</string>
    <string name="recipes_coming_soon">Recipe Management</string>
    <string name="recipes_description">Create and manage recipes with ingredient lists, batch sizes, and cooking instructions.</string>
    <string name="recipes_subtitle">Manage your bakery recipes</string>
    <string name="recipes_title">Recipes</string>
    <string name="retry">Retry</string>
    <string name="save">Save</string>
    <string name="search">Search</string>
    <string name="settings_account">Account</string>
    <string name="settings_appearance">Appearance</string>
    <string name="settings_dark_mode">Dark Mode</string>
    <string name="settings_dark_mode_disabled">Disabled</string>
    <string name="settings_dark_mode_enabled">Enabled</string>
    <string name="settings_demo_features">Demo Features</string>
    <string name="settings_logout">Logout</string>
    <string name="settings_role_format">Role: %1$s</string>
    <string name="settings_subtitle">App preferences and configuration</string>
    <string name="settings_switch_to_admin">Switch to Admin</string>
    <string name="settings_switch_to_staff">Switch to Staff</string>
    <string name="settings_title">Settings</string>
    <string name="settings_toggle_role">Toggle User Role</string>
    <string name="settings_toggle_role_desc">Switch between Admin and Staff</string>
    <string name="settings_user_info">User Information</string>
    <string name="sort">Sort</string>
    <string name="staff_coming_soon">Staff Management</string>
    <string name="staff_description">Manage staff roles, permissions, and access control for different features.</string>
    <string name="staff_subtitle">Manage staff and permissions</string>
    <string name="staff_title">Staff</string>
    <string name="stats_orders_pending">pending</string>
    <string name="stats_orders_title">Orders</string>
    <string name="stats_production_title">Today\'s Production</string>
    <string name="stats_production_units">units</string>
    <string name="stats_revenue_title">Revenue</string>
    <string name="stats_revenue_today">today</string>
    <string name="stats_stock_low">low stock</string>
    <string name="stats_stock_title">Stock Items</string>
    <string name="stock_add">Add Ingredient</string>
    <string name="stock_coming_soon">Stock Management</string>
    <string name="stock_description">Track ingredient quantities, set low-stock thresholds, manage prices, and import/export CSV data.</string>
    <string name="stock_subtitle">Manage ingredient inventory</string>
    <string name="stock_title">Stock</string>
    <style name="Theme.BakeryTracker" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/bakery_primary</item>
        <item name="colorOnPrimary">@color/bakery_on_primary</item>
        <item name="colorPrimaryContainer">@color/bakery_secondary</item>
        <item name="colorOnPrimaryContainer">@color/bakery_on_secondary</item>
        <item name="colorSecondary">@color/bakery_secondary</item>
        <item name="colorOnSecondary">@color/bakery_on_secondary</item>
        <item name="colorSecondaryContainer">@color/bakery_secondary_variant</item>
        <item name="colorOnSecondaryContainer">@color/bakery_on_secondary</item>
        <item name="colorTertiary">@color/low_stock_warning</item>
        <item name="colorOnTertiary">@color/bakery_on_primary</item>
        <item name="colorError">@color/bakery_error</item>
        <item name="colorOnError">@color/bakery_on_error</item>
        <item name="colorBackground">@color/bakery_background</item>
        <item name="colorOnBackground">@color/bakery_on_background</item>
        <item name="colorSurface">@color/bakery_surface</item>
        <item name="colorOnSurface">@color/bakery_on_surface</item>
        <item name="android:statusBarColor">?attr/colorPrimary</item>
    </style>
</resources>